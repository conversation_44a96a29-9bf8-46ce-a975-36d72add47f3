import axios from 'axios';
import Cookies from 'js-cookie';
import { toast } from 'react-toastify';

// Create axios instance
const api = axios.create({
  baseURL: import.meta.env.VITE_API_URL || 'http://localhost:5001/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = Cookies.get('token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Response interceptor to handle errors
api.interceptors.response.use(
  (response) => {
    return response;
  },
  (error) => {
    // Handle different error status codes
    if (error.response) {
      const { status, data } = error.response;
      
      switch (status) {
        case 401:
          // Unauthorized - clear token and redirect to login
          Cookies.remove('token');
          if (window.location.pathname !== '/login' && window.location.pathname !== '/register') {
            toast.error('Session expired. Please login again.');
            window.location.href = '/login';
          }
          break;
          
        case 403:
          toast.error('You do not have permission to perform this action.');
          break;
          
        case 404:
          toast.error('Resource not found.');
          break;
          
        case 422:
          // Validation errors
          if (data.errors && Array.isArray(data.errors)) {
            data.errors.forEach(err => toast.error(err.msg));
          } else {
            toast.error(data.message || 'Validation error.');
          }
          break;
          
        case 429:
          toast.error('Too many requests. Please try again later.');
          break;
          
        case 500:
          toast.error('Server error. Please try again later.');
          break;
          
        default:
          toast.error(data.message || 'An unexpected error occurred.');
      }
    } else if (error.request) {
      // Network error
      toast.error('Network error. Please check your connection.');
    } else {
      // Other error
      toast.error('An unexpected error occurred.');
    }
    
    return Promise.reject(error);
  }
);

// API endpoints
export const authAPI = {
  login: (credentials) => api.post('/auth/login', credentials),
  register: (userData) => api.post('/auth/register', userData),
  logout: () => api.post('/auth/logout'),
  getMe: () => api.get('/auth/me'),
  updateProfile: (userData) => api.patch('/auth/update-me', userData),
  updatePassword: (passwordData) => api.patch('/auth/update-password', passwordData),
  forgotPassword: (email) => api.post('/auth/forgot-password', { email }),
  resetPassword: (token, passwordData) => api.patch(`/auth/reset-password/${token}`, passwordData),
  verifyEmail: (token) => api.get(`/auth/verify-email/${token}`),
  resendVerification: (email) => api.post('/auth/resend-verification', { email }),
};

export const ganeshAPI = {
  getAll: (params) => api.get('/ganesh', { params }),
  getById: (id) => api.get(`/ganesh/${id}`),
  getBySlug: (slug) => api.get(`/ganesh/slug/${slug}`),
  search: (params) => api.get('/ganesh/search', { params }),
  getFeatured: (params) => api.get('/ganesh/featured', { params }),
  getByCategory: (category, params) => api.get(`/ganesh/categories/${category}`, { params }),
  getMyListings: (params) => api.get('/ganesh/my/listings', { params }),
  create: (ganeshData) => api.post('/ganesh', ganeshData),
  update: (id, ganeshData) => api.patch(`/ganesh/${id}`, ganeshData),
  delete: (id) => api.delete(`/ganesh/${id}`),
  addReview: (id, reviewData) => api.post(`/ganesh/${id}/reviews`, reviewData),
  updateReview: (id, reviewId, reviewData) => api.patch(`/ganesh/${id}/reviews/${reviewId}`, reviewData),
  deleteReview: (id, reviewId) => api.delete(`/ganesh/${id}/reviews/${reviewId}`),
  like: (id) => api.post(`/ganesh/${id}/like`),
  unlike: (id) => api.delete(`/ganesh/${id}/like`),
  // Admin functions
  approve: (id) => api.patch(`/ganesh/${id}/approve`),
  reject: (id, reason) => api.patch(`/ganesh/${id}/reject`, { reason }),
  toggleFeature: (id) => api.patch(`/ganesh/${id}/feature`),
};

export const userAPI = {
  getAll: (params) => api.get('/users', { params }),
  getById: (id) => api.get(`/users/${id}`),
  update: (id, userData) => api.patch(`/users/${id}`, userData),
  delete: (id) => api.delete(`/users/${id}`),
};

// Utility functions
export const uploadImage = async (file) => {
  const formData = new FormData();
  formData.append('image', file);
  
  try {
    const response = await api.post('/upload/image', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    });
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const healthCheck = () => api.get('/health');

export default api;
