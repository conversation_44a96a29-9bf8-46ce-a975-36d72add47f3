import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Paper,
  Stepper,
  Step,
  StepLabel,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  Alert,
} from '@mui/material';
import { ArrowBack, Save } from '@mui/icons-material';

const CreateListing = () => {
  const location = useLocation();
  const navigate = useNavigate();
  const quickData = location.state?.quickData;

  const [activeStep, setActiveStep] = useState(0);
  const [formData, setFormData] = useState({
    title: quickData?.title || '',
    category: quickData?.category || '',
    description: quickData?.description || '',
    city: quickData?.city || '',
    address: '',
    state: '',
    pincode: '',
    installationDate: '',
    visarjanDate: '',
    openTime: '06:00',
    closeTime: '22:00',
  });

  const steps = ['Basic Information', 'Location Details', 'Festival Details', 'Review & Submit'];

  const handleNext = () => {
    setActiveStep((prevActiveStep) => prevActiveStep + 1);
  };

  const handleBack = () => {
    setActiveStep((prevActiveStep) => prevActiveStep - 1);
  };

  const handleChange = (field, value) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const renderStepContent = (step) => {
    switch (step) {
      case 0:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Title"
                value={formData.title}
                onChange={(e) => handleChange('title', e.target.value)}
                required
                placeholder="e.g., Lalbaugcha Raja, Home Ganesha"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth required>
                <InputLabel>Category</InputLabel>
                <Select
                  value={formData.category}
                  onChange={(e) => handleChange('category', e.target.value)}
                  label="Category"
                >
                  <MenuItem value="home">Home Ganesha</MenuItem>
                  <MenuItem value="mandal">Mandal Ganesha</MenuItem>
                  <MenuItem value="celebrity">Celebrity Ganesha</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Description"
                value={formData.description}
                onChange={(e) => handleChange('description', e.target.value)}
                multiline
                rows={4}
                required
                placeholder="Describe your Ganesha installation..."
              />
            </Grid>
          </Grid>
        );
      case 1:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Address"
                value={formData.address}
                onChange={(e) => handleChange('address', e.target.value)}
                required
                placeholder="Full address"
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="City"
                value={formData.city}
                onChange={(e) => handleChange('city', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="State"
                value={formData.state}
                onChange={(e) => handleChange('state', e.target.value)}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Pincode"
                value={formData.pincode}
                onChange={(e) => handleChange('pincode', e.target.value)}
                required
                inputProps={{ pattern: '[0-9]{6}' }}
              />
            </Grid>
          </Grid>
        );
      case 2:
        return (
          <Grid container spacing={3}>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Installation Date"
                type="date"
                value={formData.installationDate}
                onChange={(e) => handleChange('installationDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Visarjan Date"
                type="date"
                value={formData.visarjanDate}
                onChange={(e) => handleChange('visarjanDate', e.target.value)}
                InputLabelProps={{ shrink: true }}
                required
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Opening Time"
                type="time"
                value={formData.openTime}
                onChange={(e) => handleChange('openTime', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="Closing Time"
                type="time"
                value={formData.closeTime}
                onChange={(e) => handleChange('closeTime', e.target.value)}
                InputLabelProps={{ shrink: true }}
              />
            </Grid>
          </Grid>
        );
      case 3:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              Review Your Listing
            </Typography>
            <Alert severity="info" sx={{ mb: 2 }}>
              This is a preview of your listing. The full create listing functionality is under development.
            </Alert>
            <Typography variant="body2" paragraph>
              <strong>Title:</strong> {formData.title}
            </Typography>
            <Typography variant="body2" paragraph>
              <strong>Category:</strong> {formData.category}
            </Typography>
            <Typography variant="body2" paragraph>
              <strong>Location:</strong> {formData.address}, {formData.city}, {formData.state} - {formData.pincode}
            </Typography>
            <Typography variant="body2" paragraph>
              <strong>Description:</strong> {formData.description}
            </Typography>
          </Box>
        );
      default:
        return 'Unknown step';
    }
  };

  return (
    <Container maxWidth="md" sx={{ py: 4 }}>
      <Box sx={{ mb: 3 }}>
        <Button
          onClick={() => navigate('/dashboard')}
          startIcon={<ArrowBack />}
          sx={{ mb: 2 }}
        >
          Back to Dashboard
        </Button>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          Create New Listing
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Share your Ganesha installation with devotees
        </Typography>
      </Box>

      <Paper sx={{ p: 4 }}>
        <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
          {steps.map((label) => (
            <Step key={label}>
              <StepLabel>{label}</StepLabel>
            </Step>
          ))}
        </Stepper>

        <Box sx={{ mb: 4 }}>
          {renderStepContent(activeStep)}
        </Box>

        <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
          <Button
            disabled={activeStep === 0}
            onClick={handleBack}
          >
            Back
          </Button>
          <Button
            variant="contained"
            onClick={activeStep === steps.length - 1 ? () => {
              alert('Full create listing functionality coming soon!');
              navigate('/dashboard');
            } : handleNext}
            startIcon={activeStep === steps.length - 1 ? <Save /> : null}
          >
            {activeStep === steps.length - 1 ? 'Submit Listing' : 'Next'}
          </Button>
        </Box>
      </Paper>
    </Container>
  );
};

export default CreateListing;
