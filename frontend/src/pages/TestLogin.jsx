import React, { useState } from 'react';
import { Container, Paper, TextField, Button, Typography, Box } from '@mui/material';

const TestLogin = () => {
  const [email, setEmail] = useState('<EMAIL>');
  const [password, setPassword] = useState('Test123456');
  const [message, setMessage] = useState('');

  const handleLogin = async () => {
    try {
      setMessage('Logging in...');
      
      const response = await fetch('http://localhost:5001/api/auth/login', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({ email, password })
      });

      const data = await response.json();
      
      if (response.ok) {
        setMessage('✅ LOGIN SUCCESS! Token: ' + data.token.substring(0, 20) + '...');
        localStorage.setItem('token', data.token);
        localStorage.setItem('user', JSON.stringify(data.data.user));
      } else {
        setMessage('❌ LOGIN FAILED: ' + data.message);
      }
    } catch (error) {
      setMessage('❌ ERROR: ' + error.message);
    }
  };

  const handleRegister = async () => {
    try {
      setMessage('Registering...');
      
      const response = await fetch('http://localhost:5001/api/auth/register', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json'
        },
        body: JSON.stringify({
          firstName: 'Test',
          lastName: 'User',
          email: email,
          password: password
        })
      });

      const data = await response.json();
      
      if (response.ok) {
        setMessage('✅ REGISTER SUCCESS! Now try login.');
      } else {
        setMessage('❌ REGISTER FAILED: ' + data.message);
      }
    } catch (error) {
      setMessage('❌ ERROR: ' + error.message);
    }
  };

  return (
    <Container maxWidth="sm" sx={{ py: 4 }}>
      <Paper sx={{ p: 4 }}>
        <Typography variant="h4" gutterBottom>
          🧪 Test Login
        </Typography>
        
        <TextField
          fullWidth
          label="Email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          margin="normal"
        />
        
        <TextField
          fullWidth
          label="Password"
          type="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          margin="normal"
        />
        
        <Box sx={{ mt: 2, display: 'flex', gap: 2 }}>
          <Button variant="contained" onClick={handleLogin} fullWidth>
            LOGIN
          </Button>
          <Button variant="outlined" onClick={handleRegister} fullWidth>
            REGISTER
          </Button>
        </Box>
        
        {message && (
          <Box sx={{ mt: 2, p: 2, bgcolor: 'grey.100', borderRadius: 1 }}>
            <Typography variant="body2">
              {message}
            </Typography>
          </Box>
        )}
      </Paper>
    </Container>
  );
};

export default TestLogin;
