import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  CardActions,
  Button,
  Chip,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Pagination,
  CircularProgress,
  Alert,
  InputAdornment,
  IconButton,
  Paper,
  Tabs,
  Tab,
  Rating,
} from '@mui/material';
import {
  Search,
  LocationOn,
  Favorite,
  FavoriteBorder,
  Visibility,
  Star,
  Home,
  Temple,
  FilterList,
  Clear,
} from '@mui/icons-material';
import { ganeshAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-toastify';

const GaneshListings = () => {
  const [searchParams, setSearchParams] = useSearchParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();

  const [listings, setListings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [totalPages, setTotalPages] = useState(1);
  const [currentPage, setCurrentPage] = useState(1);
  const [likedListings, setLikedListings] = useState(new Set());

  // Filter states
  const [searchQuery, setSearchQuery] = useState(searchParams.get('q') || '');
  const [selectedCategory, setSelectedCategory] = useState(searchParams.get('category') || '');
  const [selectedCity, setSelectedCity] = useState(searchParams.get('city') || '');
  const [selectedState, setSelectedState] = useState(searchParams.get('state') || '');
  const [sortBy, setSortBy] = useState(searchParams.get('sort') || 'newest');
  const [tabValue, setTabValue] = useState(0);

  const categories = [
    { value: '', label: 'All Categories' },
    { value: 'home', label: 'Home Ganesha', icon: <Home /> },
    { value: 'mandal', label: 'Mandal Ganesha', icon: <Temple /> },
    { value: 'celebrity', label: 'Celebrity Ganesha', icon: <Star /> },
  ];

  const sortOptions = [
    { value: 'newest', label: 'Newest First' },
    { value: 'oldest', label: 'Oldest First' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'views', label: 'Most Viewed' },
    { value: 'likes', label: 'Most Liked' },
  ];

  useEffect(() => {
    fetchListings();
  }, [currentPage, searchQuery, selectedCategory, selectedCity, selectedState, sortBy]);

  useEffect(() => {
    // Update URL params when filters change
    const params = new URLSearchParams();
    if (searchQuery) params.set('q', searchQuery);
    if (selectedCategory) params.set('category', selectedCategory);
    if (selectedCity) params.set('city', selectedCity);
    if (selectedState) params.set('state', selectedState);
    if (sortBy !== 'newest') params.set('sort', sortBy);
    if (currentPage > 1) params.set('page', currentPage.toString());

    setSearchParams(params);
  }, [searchQuery, selectedCategory, selectedCity, selectedState, sortBy, currentPage, setSearchParams]);

  const fetchListings = async () => {
    try {
      setLoading(true);
      setError(null);

      const params = {
        page: currentPage,
        limit: 12,
        ...(searchQuery && { q: searchQuery }),
        ...(selectedCategory && { category: selectedCategory }),
        ...(selectedCity && { city: selectedCity }),
        ...(selectedState && { state: selectedState }),
      };

      let response;
      if (searchQuery || selectedCity || selectedState) {
        response = await ganeshAPI.search(params);
      } else if (selectedCategory) {
        response = await ganeshAPI.getByCategory(selectedCategory, params);
      } else {
        response = await ganeshAPI.getAll(params);
      }

      const data = response.data.data.ganeshListings;
      setListings(data);
      setTotalPages(response.data.pagination.pages);

      // Track liked listings for authenticated users
      if (isAuthenticated && user) {
        const liked = new Set();
        data.forEach(listing => {
          const userLike = listing.likes.find(like => like.user === user._id);
          if (userLike) liked.add(listing._id);
        });
        setLikedListings(liked);
      }
    } catch (error) {
      console.error('Error fetching listings:', error);
      setError('Failed to load listings');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async (listingId) => {
    if (!isAuthenticated) {
      toast.error('Please login to like listings');
      return;
    }

    try {
      const isLiked = likedListings.has(listingId);

      if (isLiked) {
        await ganeshAPI.unlike(listingId);
        setLikedListings(prev => {
          const newSet = new Set(prev);
          newSet.delete(listingId);
          return newSet;
        });
        toast.success('Removed from favorites');
      } else {
        await ganeshAPI.like(listingId);
        setLikedListings(prev => new Set(prev).add(listingId));
        toast.success('Added to favorites');
      }

      // Update the listing in the current list
      setListings(prev => prev.map(listing => {
        if (listing._id === listingId) {
          const newLikes = isLiked
            ? listing.likes.filter(like => like.user !== user._id)
            : [...listing.likes, { user: user._id, createdAt: new Date() }];
          return { ...listing, likes: newLikes };
        }
        return listing;
      }));
    } catch (error) {
      toast.error('Failed to update like status');
    }
  };

  const handleSearch = (e) => {
    e.preventDefault();
    setCurrentPage(1);
    fetchListings();
  };

  const clearFilters = () => {
    setSearchQuery('');
    setSelectedCategory('');
    setSelectedCity('');
    setSelectedState('');
    setSortBy('newest');
    setCurrentPage(1);
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'home': return <Home />;
      case 'mandal': return <Temple />;
      case 'celebrity': return <Star />;
      default: return <Home />;
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'home': return 'primary';
      case 'mandal': return 'secondary';
      case 'celebrity': return 'success';
      default: return 'default';
    }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 4 }}>
        <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
          🙏 Explore Ganesha Listings
        </Typography>
        <Typography variant="body1" color="text.secondary">
          Discover beautiful Ganesha installations across different categories
        </Typography>
      </Box>

      {/* Category Tabs */}
      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={tabValue}
          onChange={(e, newValue) => {
            setTabValue(newValue);
            setSelectedCategory(categories[newValue].value);
            setCurrentPage(1);
          }}
          variant="scrollable"
          scrollButtons="auto"
        >
          {categories.map((category, index) => (
            <Tab
              key={index}
              icon={category.icon}
              label={category.label}
              iconPosition="start"
            />
          ))}
        </Tabs>
      </Paper>

      {/* Search and Filters */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <form onSubmit={handleSearch}>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search Ganesha listings..."
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }}
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                fullWidth
                label="City"
                value={selectedCity}
                onChange={(e) => setSelectedCity(e.target.value)}
                placeholder="e.g., Mumbai"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <TextField
                fullWidth
                label="State"
                value={selectedState}
                onChange={(e) => setSelectedState(e.target.value)}
                placeholder="e.g., Maharashtra"
              />
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth>
                <InputLabel>Sort By</InputLabel>
                <Select
                  value={sortBy}
                  onChange={(e) => setSortBy(e.target.value)}
                  label="Sort By"
                >
                  {sortOptions.map((option) => (
                    <MenuItem key={option.value} value={option.value}>
                      {option.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button type="submit" variant="contained" startIcon={<Search />}>
                  Search
                </Button>
                <IconButton onClick={clearFilters} title="Clear Filters">
                  <Clear />
                </IconButton>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>

      {/* Results */}
      {loading ? (
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress size={40} />
        </Box>
      ) : error ? (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      ) : (
        <>
          {/* Results Header */}
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
            <Typography variant="h6">
              {listings.length > 0
                ? `Found ${listings.length} listings`
                : 'No listings found'
              }
            </Typography>
            {(searchQuery || selectedCategory || selectedCity || selectedState) && (
              <Button
                variant="outlined"
                startIcon={<Clear />}
                onClick={clearFilters}
                size="small"
              >
                Clear All Filters
              </Button>
            )}
          </Box>

          {/* Listings Grid */}
          {listings.length > 0 ? (
            <>
              <Grid container spacing={3}>
                {listings.map((listing) => (
                  <Grid item xs={12} sm={6} md={4} key={listing._id}>
                    <Card
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        transition: 'transform 0.2s, box-shadow 0.2s',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                          boxShadow: 4,
                        },
                      }}
                    >
                      <CardMedia
                        component="img"
                        height="200"
                        image={listing.images[0]?.url || '/placeholder-ganesh.jpg'}
                        alt={listing.title}
                        sx={{ cursor: 'pointer' }}
                        onClick={() => navigate(`/ganesh/${listing._id}`)}
                      />
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 1 }}>
                          <Typography variant="h6" component="h3" fontWeight="bold" sx={{ flexGrow: 1 }}>
                            {listing.title}
                          </Typography>
                          <Chip
                            icon={getCategoryIcon(listing.category)}
                            label={listing.category}
                            color={getCategoryColor(listing.category)}
                            size="small"
                          />
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                          <LocationOn fontSize="small" color="action" sx={{ mr: 0.5 }} />
                          <Typography variant="body2" color="text.secondary">
                            {listing.location.city}, {listing.location.state}
                          </Typography>
                        </Box>

                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <Rating value={listing.averageRating} readOnly size="small" sx={{ mr: 1 }} />
                          <Typography variant="body2" color="text.secondary">
                            ({listing.totalReviews})
                          </Typography>
                        </Box>

                        <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                          {listing.description.length > 100
                            ? `${listing.description.substring(0, 100)}...`
                            : listing.description
                          }
                        </Typography>

                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <Visibility fontSize="small" color="action" />
                            <Typography variant="caption" color="text.secondary">
                              {listing.views}
                            </Typography>
                          </Box>
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                            <Favorite fontSize="small" color="action" />
                            <Typography variant="caption" color="text.secondary">
                              {listing.likes.length}
                            </Typography>
                          </Box>
                        </Box>
                      </CardContent>

                      <CardActions sx={{ justifyContent: 'space-between', px: 2, pb: 2 }}>
                        <Button
                          size="small"
                          onClick={() => navigate(`/ganesh/${listing._id}`)}
                        >
                          View Details
                        </Button>
                        <IconButton
                          onClick={() => handleLike(listing._id)}
                          color={likedListings.has(listing._id) ? 'error' : 'default'}
                          size="small"
                        >
                          {likedListings.has(listing._id) ? <Favorite /> : <FavoriteBorder />}
                        </IconButton>
                      </CardActions>
                    </Card>
                  </Grid>
                ))}
              </Grid>

              {/* Pagination */}
              {totalPages > 1 && (
                <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
                  <Pagination
                    count={totalPages}
                    page={currentPage}
                    onChange={(e, page) => setCurrentPage(page)}
                    color="primary"
                    size="large"
                  />
                </Box>
              )}
            </>
          ) : (
            <Paper sx={{ p: 6, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                No listings found
              </Typography>
              <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                Try adjusting your search criteria or browse all categories
              </Typography>
              <Button variant="contained" onClick={clearFilters}>
                View All Listings
              </Button>
            </Paper>
          )}
        </>
      )}
    </Container>
  );
};

export default GaneshListings;
