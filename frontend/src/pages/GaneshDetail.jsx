import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Button,
  Chip,
  Avatar,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Paper,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  ImageList,
  ImageListItem,
  Rating,
  TextField,
} from '@mui/material';
import {
  LocationOn,
  Phone,
  Email,
  CalendarToday,
  AccessTime,
  Favorite,
  FavoriteBorder,
  Share,
  Directions,
  PhotoLibrary,
  VideoLibrary,
  QrCode,
  People,
  Star,
  Send,
  ArrowBack,
  Temple,
  Home,
  Person,
} from '@mui/icons-material';
import { ganeshAPI } from '../services/api';
import { useAuth } from '../contexts/AuthContext';
import { toast } from 'react-toastify';

const GaneshDetail = () => {
  const { id } = useParams();
  const navigate = useNavigate();
  const { user, isAuthenticated } = useAuth();

  const [ganesh, setGanesh] = useState(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isLiked, setIsLiked] = useState(false);
  const [tabValue, setTabValue] = useState(0);
  const [photoDialogOpen, setPhotoDialogOpen] = useState(false);
  const [selectedPhoto, setSelectedPhoto] = useState(null);
  const [reviewText, setReviewText] = useState('');
  const [reviewRating, setReviewRating] = useState(5);

  useEffect(() => {
    fetchGaneshDetails();
  }, [id]);

  const fetchGaneshDetails = async () => {
    try {
      setLoading(true);
      const response = await ganeshAPI.getById(id);
      const ganeshData = response.data.data.ganesh;
      setGanesh(ganeshData);

      // Check if user has liked this listing
      if (isAuthenticated && user) {
        const userLike = ganeshData.likes.find(like => like.user === user._id);
        setIsLiked(!!userLike);
      }
    } catch (error) {
      console.error('Error fetching Ganesh details:', error);
      setError('Failed to load Ganesh details');
    } finally {
      setLoading(false);
    }
  };

  const handleLike = async () => {
    if (!isAuthenticated) {
      toast.error('Please login to like listings');
      return;
    }

    try {
      if (isLiked) {
        await ganeshAPI.unlike(id);
        setIsLiked(false);
        toast.success('Removed from favorites');
      } else {
        await ganeshAPI.like(id);
        setIsLiked(true);
        toast.success('Added to favorites');
      }
      // Refresh data to get updated like count
      fetchGaneshDetails();
    } catch (error) {
      toast.error('Failed to update like status');
    }
  };

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: ganesh.title,
          text: ganesh.description,
          url: window.location.href,
        });
      } catch (error) {
        console.log('Error sharing:', error);
      }
    } else {
      // Fallback: copy to clipboard
      navigator.clipboard.writeText(window.location.href);
      toast.success('Link copied to clipboard');
    }
  };

  const handleAddReview = async () => {
    if (!isAuthenticated) {
      toast.error('Please login to add reviews');
      return;
    }

    if (!reviewText.trim()) {
      toast.error('Please enter a review');
      return;
    }

    try {
      await ganeshAPI.addReview(id, {
        rating: reviewRating,
        comment: reviewText,
      });
      setReviewText('');
      setReviewRating(5);
      toast.success('Review added successfully');
      fetchGaneshDetails(); // Refresh to show new review
    } catch (error) {
      toast.error('Failed to add review');
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'home': return <Home />;
      case 'mandal': return <Temple />;
      case 'celebrity': return <Star />;
      default: return <Home />;
    }
  };

  const getCategoryColor = (category) => {
    switch (category) {
      case 'home': return 'primary';
      case 'mandal': return 'secondary';
      case 'celebrity': return 'success';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress size={40} />
        </Box>
      </Container>
    );
  }

  if (error || !ganesh) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error || 'Ganesh listing not found'}
        </Alert>
        <Button onClick={() => navigate('/ganesh')} startIcon={<ArrowBack />}>
          Back to Listings
        </Button>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Header */}
      <Box sx={{ mb: 3 }}>
        <Button
          onClick={() => navigate('/ganesh')}
          startIcon={<ArrowBack />}
          sx={{ mb: 2 }}
        >
          Back to Listings
        </Button>

        <Grid container spacing={2} alignItems="center">
          <Grid item xs>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
              <Typography variant="h4" component="h1" fontWeight="bold">
                {ganesh.title}
              </Typography>
              <Chip
                icon={getCategoryIcon(ganesh.category)}
                label={ganesh.category.charAt(0).toUpperCase() + ganesh.category.slice(1)}
                color={getCategoryColor(ganesh.category)}
              />
            </Box>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, color: 'text.secondary' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <LocationOn fontSize="small" />
                <Typography variant="body2">
                  {ganesh.location.address}, {ganesh.location.city}
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                <Star fontSize="small" />
                <Typography variant="body2">
                  {ganesh.averageRating.toFixed(1)} ({ganesh.totalReviews} reviews)
                </Typography>
              </Box>
            </Box>
          </Grid>
          <Grid item>
            <Box sx={{ display: 'flex', gap: 1 }}>
              <IconButton onClick={handleLike} color={isLiked ? 'error' : 'default'}>
                {isLiked ? <Favorite /> : <FavoriteBorder />}
              </IconButton>
              <IconButton onClick={handleShare}>
                <Share />
              </IconButton>
              <Button variant="outlined" startIcon={<Directions />}>
                Get Directions
              </Button>
            </Box>
          </Grid>
        </Grid>
      </Box>

      {/* Main Content */}
      <Grid container spacing={3}>
        {/* Left Column - Images and Main Info */}
        <Grid item xs={12} md={8}>
          {/* Image Gallery */}
          <Card sx={{ mb: 3 }}>
            <CardMedia
              component="img"
              height="400"
              image={ganesh.images[0]?.url || '/placeholder-ganesh.jpg'}
              alt={ganesh.title}
              sx={{ cursor: 'pointer' }}
              onClick={() => {
                setSelectedPhoto(ganesh.images[0]);
                setPhotoDialogOpen(true);
              }}
            />
            {ganesh.images.length > 1 && (
              <CardContent>
                <ImageList cols={4} rowHeight={100}>
                  {ganesh.images.slice(1, 5).map((image, index) => (
                    <ImageListItem
                      key={index}
                      sx={{ cursor: 'pointer' }}
                      onClick={() => {
                        setSelectedPhoto(image);
                        setPhotoDialogOpen(true);
                      }}
                    >
                      <img src={image.url} alt={`Gallery ${index + 1}`} loading="lazy" />
                    </ImageListItem>
                  ))}
                </ImageList>
                {ganesh.images.length > 5 && (
                  <Button
                    fullWidth
                    startIcon={<PhotoLibrary />}
                    sx={{ mt: 1 }}
                    onClick={() => setPhotoDialogOpen(true)}
                  >
                    View All {ganesh.images.length} Photos
                  </Button>
                )}
              </CardContent>
            )}
          </Card>

          {/* Tabs for Different Information */}
          <Paper sx={{ mb: 3 }}>
            <Tabs value={tabValue} onChange={(e, newValue) => setTabValue(newValue)}>
              <Tab label="Details" />
              <Tab label="Festival Info" />
              {ganesh.category === 'mandal' && <Tab label="Mandal Info" />}
              {ganesh.category === 'celebrity' && <Tab label="Celebrity Info" />}
              <Tab label="Reviews" />
            </Tabs>

            <Box sx={{ p: 3 }}>
              {/* Details Tab */}
              {tabValue === 0 && (
                <Box>
                  <Typography variant="h6" gutterBottom fontWeight="bold">
                    Description
                  </Typography>
                  <Typography variant="body1" paragraph>
                    {ganesh.description}
                  </Typography>

                  <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ mt: 3 }}>
                    Location Details
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemIcon><LocationOn /></ListItemIcon>
                      <ListItemText
                        primary="Address"
                        secondary={`${ganesh.location.address}, ${ganesh.location.city}, ${ganesh.location.state} - ${ganesh.location.pincode}`}
                      />
                    </ListItem>
                    {ganesh.location.landmark && (
                      <ListItem>
                        <ListItemIcon><LocationOn /></ListItemIcon>
                        <ListItemText
                          primary="Landmark"
                          secondary={ganesh.location.landmark}
                        />
                      </ListItem>
                    )}
                  </List>

                  <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ mt: 3 }}>
                    Visiting Hours
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemIcon><AccessTime /></ListItemIcon>
                      <ListItemText
                        primary="Timings"
                        secondary={`${ganesh.visitingHours.openTime} - ${ganesh.visitingHours.closeTime}`}
                      />
                    </ListItem>
                    {ganesh.visitingHours.specialNote && (
                      <ListItem>
                        <ListItemText
                          primary="Special Note"
                          secondary={ganesh.visitingHours.specialNote}
                        />
                      </ListItem>
                    )}
                  </List>

                  {ganesh.facilities && ganesh.facilities.length > 0 && (
                    <>
                      <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ mt: 3 }}>
                        Facilities Available
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {ganesh.facilities.map((facility, index) => (
                          <Chip
                            key={index}
                            label={facility.replace('_', ' ').toUpperCase()}
                            variant="outlined"
                            size="small"
                          />
                        ))}
                      </Box>
                    </>
                  )}
                </Box>
              )}

              {/* Festival Info Tab */}
              {tabValue === 1 && (
                <Box>
                  <Typography variant="h6" gutterBottom fontWeight="bold">
                    Festival Information
                  </Typography>
                  <List>
                    <ListItem>
                      <ListItemIcon><CalendarToday /></ListItemIcon>
                      <ListItemText
                        primary="Installation Date"
                        secondary={new Date(ganesh.festivalDetails.installationDate).toLocaleDateString()}
                      />
                    </ListItem>
                    <ListItem>
                      <ListItemIcon><CalendarToday /></ListItemIcon>
                      <ListItemText
                        primary="Visarjan Date"
                        secondary={new Date(ganesh.festivalDetails.visarjanDate).toLocaleDateString()}
                      />
                    </ListItem>
                    {ganesh.festivalDetails.aarti && (
                      <>
                        <ListItem>
                          <ListItemIcon><AccessTime /></ListItemIcon>
                          <ListItemText
                            primary="Morning Aarti"
                            secondary={ganesh.festivalDetails.aarti.morning}
                          />
                        </ListItem>
                        <ListItem>
                          <ListItemIcon><AccessTime /></ListItemIcon>
                          <ListItemText
                            primary="Evening Aarti"
                            secondary={ganesh.festivalDetails.aarti.evening}
                          />
                        </ListItem>
                      </>
                    )}
                  </List>

                  {ganesh.festivalDetails.specialEvents && ganesh.festivalDetails.specialEvents.length > 0 && (
                    <>
                      <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ mt: 3 }}>
                        Special Events
                      </Typography>
                      {ganesh.festivalDetails.specialEvents.map((event, index) => (
                        <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                          <CardContent>
                            <Typography variant="subtitle1" fontWeight="bold">
                              {event.name}
                            </Typography>
                            <Typography variant="body2" color="text.secondary">
                              {new Date(event.date).toLocaleDateString()} at {event.time}
                            </Typography>
                            <Typography variant="body2" sx={{ mt: 1 }}>
                              {event.description}
                            </Typography>
                          </CardContent>
                        </Card>
                      ))}
                    </>
                  )}

                  {ganesh.festivalDetails.prasad && (
                    <>
                      <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ mt: 3 }}>
                        Prasad Information
                      </Typography>
                      <Typography variant="body2" paragraph>
                        Prasad Available: {ganesh.festivalDetails.prasad.available ? 'Yes' : 'No'}
                      </Typography>
                      {ganesh.festivalDetails.prasad.types && ganesh.festivalDetails.prasad.types.length > 0 && (
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                          {ganesh.festivalDetails.prasad.types.map((type, index) => (
                            <Chip key={index} label={type} color="secondary" size="small" />
                          ))}
                        </Box>
                      )}
                    </>
                  )}
                </Box>
              )}

              {/* Mandal Info Tab */}
              {tabValue === 2 && ganesh.category === 'mandal' && (
                <Box>
                  <Typography variant="h6" gutterBottom fontWeight="bold">
                    Mandal Information
                  </Typography>
                  {ganesh.mandalInfo && (
                    <List>
                      <ListItem>
                        <ListItemIcon><Temple /></ListItemIcon>
                        <ListItemText
                          primary="Mandal Name"
                          secondary={ganesh.mandalInfo.name}
                        />
                      </ListItem>
                      {ganesh.mandalInfo.establishedYear && (
                        <ListItem>
                          <ListItemIcon><CalendarToday /></ListItemIcon>
                          <ListItemText
                            primary="Established Year"
                            secondary={ganesh.mandalInfo.establishedYear}
                          />
                        </ListItem>
                      )}
                      {ganesh.mandalInfo.presidentName && (
                        <ListItem>
                          <ListItemIcon><Person /></ListItemIcon>
                          <ListItemText
                            primary="President"
                            secondary={ganesh.mandalInfo.presidentName}
                          />
                        </ListItem>
                      )}
                      {ganesh.mandalInfo.contactPerson && (
                        <>
                          <ListItem>
                            <ListItemIcon><Person /></ListItemIcon>
                            <ListItemText
                              primary="Contact Person"
                              secondary={ganesh.mandalInfo.contactPerson.name}
                            />
                          </ListItem>
                          {ganesh.mandalInfo.contactPerson.phone && (
                            <ListItem>
                              <ListItemIcon><Phone /></ListItemIcon>
                              <ListItemText
                                primary="Contact Phone"
                                secondary={ganesh.mandalInfo.contactPerson.phone}
                              />
                            </ListItem>
                          )}
                          {ganesh.mandalInfo.contactPerson.email && (
                            <ListItem>
                              <ListItemIcon><Email /></ListItemIcon>
                              <ListItemText
                                primary="Contact Email"
                                secondary={ganesh.mandalInfo.contactPerson.email}
                              />
                            </ListItem>
                          )}
                        </>
                      )}
                    </List>
                  )}

                  {ganesh.mandalInfo?.socialMedia && (
                    <>
                      <Typography variant="h6" gutterBottom fontWeight="bold" sx={{ mt: 3 }}>
                        Social Media
                      </Typography>
                      <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1 }}>
                        {ganesh.mandalInfo.socialMedia.facebook && (
                          <Button
                            variant="outlined"
                            size="small"
                            href={ganesh.mandalInfo.socialMedia.facebook}
                            target="_blank"
                          >
                            Facebook
                          </Button>
                        )}
                        {ganesh.mandalInfo.socialMedia.instagram && (
                          <Button
                            variant="outlined"
                            size="small"
                            href={ganesh.mandalInfo.socialMedia.instagram}
                            target="_blank"
                          >
                            Instagram
                          </Button>
                        )}
                        {ganesh.mandalInfo.socialMedia.youtube && (
                          <Button
                            variant="outlined"
                            size="small"
                            href={ganesh.mandalInfo.socialMedia.youtube}
                            target="_blank"
                          >
                            YouTube
                          </Button>
                        )}
                      </Box>
                    </>
                  )}
                </Box>
              )}

              {/* Celebrity Info Tab */}
              {tabValue === (ganesh.category === 'mandal' ? 3 : 2) && ganesh.category === 'celebrity' && (
                <Box>
                  <Typography variant="h6" gutterBottom fontWeight="bold">
                    Celebrity Information
                  </Typography>
                  {ganesh.celebrityInfo && (
                    <List>
                      <ListItem>
                        <ListItemIcon><Star /></ListItemIcon>
                        <ListItemText
                          primary="Celebrity Name"
                          secondary={ganesh.celebrityInfo.celebrityName}
                        />
                      </ListItem>
                      {ganesh.celebrityInfo.profession && (
                        <ListItem>
                          <ListItemIcon><Person /></ListItemIcon>
                          <ListItemText
                            primary="Profession"
                            secondary={ganesh.celebrityInfo.profession}
                          />
                        </ListItem>
                      )}
                      <ListItem>
                        <ListItemIcon><Star /></ListItemIcon>
                        <ListItemText
                          primary="Verified"
                          secondary={ganesh.celebrityInfo.verified ? 'Yes' : 'No'}
                        />
                      </ListItem>
                    </List>
                  )}
                </Box>
              )}

              {/* Reviews Tab */}
              {tabValue === (ganesh.category === 'mandal' ? 4 : ganesh.category === 'celebrity' ? 3 : 2) && (
                <Box>
                  <Typography variant="h6" gutterBottom fontWeight="bold">
                    Reviews & Ratings
                  </Typography>

                  {/* Add Review Section */}
                  {isAuthenticated && (
                    <Paper variant="outlined" sx={{ p: 2, mb: 3 }}>
                      <Typography variant="subtitle1" gutterBottom>
                        Add Your Review
                      </Typography>
                      <Box sx={{ mb: 2 }}>
                        <Typography component="legend">Rating</Typography>
                        <Rating
                          value={reviewRating}
                          onChange={(event, newValue) => setReviewRating(newValue)}
                        />
                      </Box>
                      <TextField
                        fullWidth
                        multiline
                        rows={3}
                        placeholder="Share your experience..."
                        value={reviewText}
                        onChange={(e) => setReviewText(e.target.value)}
                        sx={{ mb: 2 }}
                      />
                      <Button
                        variant="contained"
                        startIcon={<Send />}
                        onClick={handleAddReview}
                        disabled={!reviewText.trim()}
                      >
                        Submit Review
                      </Button>
                    </Paper>
                  )}

                  {/* Reviews List */}
                  {ganesh.reviews && ganesh.reviews.length > 0 ? (
                    ganesh.reviews.map((review, index) => (
                      <Card key={index} variant="outlined" sx={{ mb: 2 }}>
                        <CardContent>
                          <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                            <Avatar sx={{ mr: 2 }}>
                              {review.user.firstName?.charAt(0)}
                            </Avatar>
                            <Box sx={{ flexGrow: 1 }}>
                              <Typography variant="subtitle2">
                                {review.user.firstName} {review.user.lastName}
                              </Typography>
                              <Rating value={review.rating} readOnly size="small" />
                            </Box>
                            <Typography variant="caption" color="text.secondary">
                              {new Date(review.createdAt).toLocaleDateString()}
                            </Typography>
                          </Box>
                          <Typography variant="body2">
                            {review.comment}
                          </Typography>
                        </CardContent>
                      </Card>
                    ))
                  ) : (
                    <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 4 }}>
                      No reviews yet. Be the first to review!
                    </Typography>
                  )}
                </Box>
              )}
            </Box>
          </Paper>
        </Grid>

        {/* Right Column - Quick Info & Actions */}
        <Grid item xs={12} md={4}>
          {/* Quick Info Card */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                Quick Information
              </Typography>
              <List dense>
                <ListItem>
                  <ListItemText
                    primary="Category"
                    secondary={ganesh.category.charAt(0).toUpperCase() + ganesh.category.slice(1)}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Views"
                    secondary={ganesh.views.toLocaleString()}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Likes"
                    secondary={ganesh.likes.length.toLocaleString()}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Rating"
                    secondary={`${ganesh.averageRating.toFixed(1)}/5 (${ganesh.totalReviews} reviews)`}
                  />
                </ListItem>
                <ListItem>
                  <ListItemText
                    primary="Status"
                    secondary={
                      <Chip
                        label={ganesh.status}
                        color={ganesh.status === 'approved' ? 'success' : 'warning'}
                        size="small"
                      />
                    }
                  />
                </ListItem>
              </List>
            </CardContent>
          </Card>

          {/* Owner Information */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                Listed By
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  src={ganesh.owner.avatar}
                  sx={{ mr: 2, width: 56, height: 56 }}
                >
                  {ganesh.owner.firstName?.charAt(0)}
                </Avatar>
                <Box>
                  <Typography variant="subtitle1" fontWeight="medium">
                    {ganesh.owner.firstName} {ganesh.owner.lastName}
                  </Typography>
                  <Typography variant="body2" color="text.secondary">
                    Member since {new Date(ganesh.owner.createdAt).getFullYear()}
                  </Typography>
                </Box>
              </Box>

              {ganesh.owner.phone && (
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<Phone />}
                  href={`tel:${ganesh.owner.phone}`}
                  sx={{ mb: 1 }}
                >
                  Call Owner
                </Button>
              )}

              {ganesh.owner.email && (
                <Button
                  fullWidth
                  variant="outlined"
                  startIcon={<Email />}
                  href={`mailto:${ganesh.owner.email}`}
                >
                  Email Owner
                </Button>
              )}
            </CardContent>
          </Card>

          {/* Action Buttons */}
          <Card sx={{ mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom fontWeight="bold">
                Actions
              </Typography>

              <Button
                fullWidth
                variant="contained"
                startIcon={<VideoLibrary />}
                sx={{ mb: 2 }}
                onClick={() => toast.info('Live Darshan feature coming soon!')}
              >
                Live Darshan
              </Button>

              <Button
                fullWidth
                variant="outlined"
                startIcon={<QrCode />}
                sx={{ mb: 2 }}
                onClick={() => toast.info('Donation QR feature coming soon!')}
              >
                Donation QR
              </Button>

              <Button
                fullWidth
                variant="outlined"
                startIcon={<PhotoLibrary />}
                onClick={() => setPhotoDialogOpen(true)}
              >
                View Photo Album
              </Button>
            </CardContent>
          </Card>

          {/* Member Details (for Mandal) */}
          {ganesh.category === 'mandal' && ganesh.mandalInfo && (
            <Card>
              <CardContent>
                <Typography variant="h6" gutterBottom fontWeight="bold">
                  <People sx={{ mr: 1, verticalAlign: 'middle' }} />
                  Team Members
                </Typography>

                {ganesh.mandalInfo.presidentName && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" fontWeight="medium">
                      President
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {ganesh.mandalInfo.presidentName}
                    </Typography>
                  </Box>
                )}

                {ganesh.mandalInfo.contactPerson && (
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="subtitle2" fontWeight="medium">
                      Contact Person
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      {ganesh.mandalInfo.contactPerson.name}
                    </Typography>
                    {ganesh.mandalInfo.contactPerson.phone && (
                      <Typography variant="body2" color="text.secondary">
                        📞 {ganesh.mandalInfo.contactPerson.phone}
                      </Typography>
                    )}
                  </Box>
                )}

                <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
                  More member details available on request
                </Typography>
              </CardContent>
            </Card>
          )}
        </Grid>
      </Grid>

      {/* Photo Dialog */}
      <Dialog
        open={photoDialogOpen}
        onClose={() => setPhotoDialogOpen(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Photo Gallery - {ganesh.title}
        </DialogTitle>
        <DialogContent>
          {ganesh.images && ganesh.images.length > 0 ? (
            <ImageList cols={2} rowHeight={300}>
              {ganesh.images.map((image, index) => (
                <ImageListItem key={index}>
                  <img
                    src={image.url}
                    alt={image.caption || `Gallery ${index + 1}`}
                    loading="lazy"
                    style={{ width: '100%', height: '100%', objectFit: 'cover' }}
                  />
                </ImageListItem>
              ))}
            </ImageList>
          ) : (
            <Typography variant="body2" color="text.secondary" textAlign="center" sx={{ py: 4 }}>
              No photos available
            </Typography>
          )}
        </DialogContent>
      </Dialog>
    </Container>
  );
};

export default GaneshDetail;
