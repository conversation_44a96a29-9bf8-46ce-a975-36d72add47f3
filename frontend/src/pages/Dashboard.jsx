import React, { useState, useEffect } from 'react';
import {
  Container,
  Typo<PERSON>,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  Paper,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  Home as HomeIcon,
  Temple as TempleIcon,
  Star as StarIcon,
  Visibility as ViewIcon,
  ThumbUp as LikeIcon,
  LocationOn as LocationIcon,
  Email as EmailIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ganeshAPI } from '../services/api';

const Dashboard = () => {
  const { user } = useAuth();
  const [myListings, setMyListings] = useState([]);
  const [loading, setLoading] = useState(true);
  const [stats, setStats] = useState({
    totalListings: 0,
    totalViews: 0,
    totalLikes: 0,
    pendingApproval: 0,
  });

  useEffect(() => {
    console.log('Dashboard mounted, user:', user);
    if (user) {
      fetchMyListings();
    }
  }, [user]);

  const fetchMyListings = async () => {
    try {
      setLoading(true);
      const response = await ganeshAPI.getMyListings({ limit: 5 });
      const listings = response.data.data.ganeshListings;
      setMyListings(listings);

      // Calculate stats
      const totalViews = listings.reduce((sum, listing) => sum + listing.views, 0);
      const totalLikes = listings.reduce((sum, listing) => sum + listing.likes.length, 0);
      const pendingApproval = listings.filter(listing => listing.status === 'pending').length;

      setStats({
        totalListings: listings.length,
        totalViews,
        totalLikes,
        pendingApproval,
      });
    } catch (error) {
      console.error('Error fetching listings:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status) => {
    switch (status) {
      case 'approved': return 'success';
      case 'pending': return 'warning';
      case 'rejected': return 'error';
      default: return 'default';
    }
  };

  const getCategoryIcon = (category) => {
    switch (category) {
      case 'home': return <HomeIcon />;
      case 'mandal': return <TempleIcon />;
      case 'celebrity': return <StarIcon />;
      default: return <HomeIcon />;
    }
  };

  if (loading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress size={40} />
        </Box>
      </Container>
    );
  }

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item>
            <Avatar
              src={user?.avatar}
              alt={user?.fullName}
              sx={{ width: 80, height: 80 }}
            >
              {user?.firstName?.charAt(0)}
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" component="h1" gutterBottom>
              Welcome back, {user?.firstName}! 🙏
            </Typography>
            <Typography variant="body1" color="text.secondary" gutterBottom>
              Manage your Ganesha listings and connect with devotees
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
              {user?.isEmailVerified ? (
                <Chip
                  icon={<CheckCircleIcon />}
                  label="Email Verified"
                  color="success"
                  size="small"
                />
              ) : (
                <Chip
                  icon={<WarningIcon />}
                  label="Email Not Verified"
                  color="warning"
                  size="small"
                />
              )}
            </Box>
          </Grid>
          <Grid item>
            <Button
              component={RouterLink}
              to="/create-listing"
              variant="contained"
              startIcon={<AddIcon />}
              size="large"
            >
              Add New Listing
            </Button>
          </Grid>
        </Grid>
      </Box>

      {/* Email Verification Alert */}
      {!user?.isEmailVerified && (
        <Alert severity="warning" sx={{ mb: 4 }}>
          <Typography variant="body2">
            Please verify your email address to create listings and interact with the community.
            Check your inbox for the verification email.
          </Typography>
        </Alert>
      )}

      {/* Stats Cards */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="primary" fontWeight="bold">
                {stats.totalListings}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Listings
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="info.main" fontWeight="bold">
                {stats.totalViews}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Views
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="error.main" fontWeight="bold">
                {stats.totalLikes}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Total Likes
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} sm={6} md={3}>
          <Card>
            <CardContent sx={{ textAlign: 'center' }}>
              <Typography variant="h4" color="warning.main" fontWeight="bold">
                {stats.pendingApproval}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Pending Approval
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>

      <Grid container spacing={3}>
        {/* Recent Listings */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
              <Typography variant="h6" fontWeight="bold">
                Recent Listings
              </Typography>
              <Button
                component={RouterLink}
                to="/my-listings"
                variant="outlined"
                size="small"
              >
                View All
              </Button>
            </Box>

            {myListings.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <Typography variant="body1" color="text.secondary" gutterBottom>
                  You haven't created any listings yet.
                </Typography>
                <Button
                  component={RouterLink}
                  to="/create-listing"
                  variant="contained"
                  startIcon={<AddIcon />}
                  sx={{ mt: 2 }}
                >
                  Create Your First Listing
                </Button>
              </Box>
            ) : (
              <List>
                {myListings.map((listing, index) => (
                  <React.Fragment key={listing._id}>
                    <ListItem
                      sx={{
                        px: 0,
                        py: 2,
                        '&:hover': { backgroundColor: 'grey.50' },
                      }}
                    >
                      <ListItemIcon>
                        {getCategoryIcon(listing.category)}
                      </ListItemIcon>
                      <ListItemText
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                            <Typography variant="subtitle1" fontWeight="medium">
                              {listing.title}
                            </Typography>
                            <Chip
                              label={listing.status}
                              color={getStatusColor(listing.status)}
                              size="small"
                            />
                          </Box>
                        }
                        secondary={
                          <Box sx={{ mt: 1 }}>
                            <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <LocationIcon fontSize="small" color="action" />
                                <Typography variant="body2" color="text.secondary">
                                  {listing.location.city}
                                </Typography>
                              </Box>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <ViewIcon fontSize="small" color="action" />
                                <Typography variant="body2" color="text.secondary">
                                  {listing.views} views
                                </Typography>
                              </Box>
                              <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                                <LikeIcon fontSize="small" color="action" />
                                <Typography variant="body2" color="text.secondary">
                                  {listing.likes.length} likes
                                </Typography>
                              </Box>
                            </Box>
                            <Typography variant="body2" color="text.secondary">
                              {listing.description.substring(0, 100)}...
                            </Typography>
                          </Box>
                        }
                      />
                    </ListItem>
                    {index < myListings.length - 1 && <Divider />}
                  </React.Fragment>
                ))}
              </List>
            )}
          </Paper>
        </Grid>

        {/* Quick Actions */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Quick Actions
            </Typography>
            <List>
              <ListItem
                component={RouterLink}
                to="/create-listing"
                sx={{
                  px: 0,
                  py: 1,
                  '&:hover': { backgroundColor: 'grey.50' },
                  borderRadius: 1,
                }}
              >
                <ListItemIcon>
                  <AddIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Add New Listing"
                  secondary="Share your Ganesha with devotees"
                />
              </ListItem>

              <ListItem
                component={RouterLink}
                to="/my-listings"
                sx={{
                  px: 0,
                  py: 1,
                  '&:hover': { backgroundColor: 'grey.50' },
                  borderRadius: 1,
                }}
              >
                <ListItemIcon>
                  <TempleIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Manage Listings"
                  secondary="Edit and update your listings"
                />
              </ListItem>

              <ListItem
                component={RouterLink}
                to="/profile"
                sx={{
                  px: 0,
                  py: 1,
                  '&:hover': { backgroundColor: 'grey.50' },
                  borderRadius: 1,
                }}
              >
                <ListItemIcon>
                  <EmailIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Update Profile"
                  secondary="Manage your account settings"
                />
              </ListItem>

              <ListItem
                component={RouterLink}
                to="/ganesh"
                sx={{
                  px: 0,
                  py: 1,
                  '&:hover': { backgroundColor: 'grey.50' },
                  borderRadius: 1,
                }}
              >
                <ListItemIcon>
                  <ViewIcon color="primary" />
                </ListItemIcon>
                <ListItemText
                  primary="Explore Listings"
                  secondary="Discover other Ganesha installations"
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Dashboard;
