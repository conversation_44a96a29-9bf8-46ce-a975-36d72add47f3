import React from 'react';
import {
  Container,
  Typography,
  Box,
  Grid,
  Card,
  CardContent,
  Button,
  Avatar,
  Chip,
  Alert,
  CircularProgress,
} from '@mui/material';
import {
  Add as AddIcon,
  CheckCircle as CheckCircleIcon,
  ExitToApp as LogoutIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

const Dashboard = () => {
  const { user, isLoading, isAuthenticated, logout } = useAuth();
  const navigate = useNavigate();

  console.log('Dashboard render - user:', user, 'isLoading:', isLoading, 'isAuthenticated:', isAuthenticated);

  const handleLogout = async () => {
    try {
      const result = await logout();
      if (result.success) {
        navigate('/');
      }
    } catch (error) {
      console.error('Logout error:', error);
      navigate('/');
    }
  };

  if (isLoading) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Box display="flex" justifyContent="center" alignItems="center" minHeight="50vh">
          <CircularProgress size={40} />
        </Box>
      </Container>
    );
  }

  if (!user || !isAuthenticated) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error">
          Please login to access the dashboard.
        </Alert>
      </Container>
    );
  }



  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Welcome Section */}
      <Box sx={{ mb: 4 }}>
        <Grid container spacing={3} alignItems="center">
          <Grid item>
            <Avatar
              sx={{ width: 80, height: 80, bgcolor: 'primary.main' }}
            >
              <PersonIcon sx={{ fontSize: 40 }} />
            </Avatar>
          </Grid>
          <Grid item xs>
            <Typography variant="h4" component="h1" gutterBottom>
              Welcome back, {user?.firstName || 'User'}! 🙏
            </Typography>
            <Typography variant="body1" color="text.secondary" gutterBottom>
              Manage your Ganesha listings and connect with devotees
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 1 }}>
              <Chip
                icon={<CheckCircleIcon />}
                label="Active User"
                color="success"
                size="small"
              />
            </Box>
          </Grid>
          <Grid item>
            <Button
              component={RouterLink}
              to="/create-listing"
              variant="contained"
              startIcon={<AddIcon />}
              size="large"
              sx={{ mr: 2 }}
            >
              Add New Listing
            </Button>
            <Button
              onClick={handleLogout}
              variant="outlined"
              startIcon={<LogoutIcon />}
              size="large"
            >
              Logout
            </Button>
          </Grid>
        </Grid>
      </Box>



      {/* User Info Card */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Account Information
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <strong>Name:</strong> {user?.firstName} {user?.lastName}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <strong>Email:</strong> {user?.email}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                <strong>Role:</strong> {user?.role || 'User'}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                <strong>Status:</strong> {user?.isActive ? 'Active' : 'Inactive'}
              </Typography>
            </CardContent>
          </Card>
        </Grid>
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                Quick Actions
              </Typography>
              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
                <Button
                  component={RouterLink}
                  to="/create-listing"
                  variant="contained"
                  startIcon={<AddIcon />}
                  fullWidth
                >
                  Create New Listing
                </Button>
                <Button
                  component={RouterLink}
                  to="/my-listings"
                  variant="outlined"
                  fullWidth
                >
                  View My Listings
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Dashboard;
                >
                  Add New Listing
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  component={RouterLink}
                  to="/ganesh"
                  variant="outlined"
                  fullWidth
                  sx={{ py: 2 }}
                >
                  Explore Listings
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  component={RouterLink}
                  to="/my-listings"
                  variant="outlined"
                  fullWidth
                  sx={{ py: 2 }}
                >
                  My Listings
                </Button>
              </Grid>
              <Grid item xs={12} sm={6} md={3}>
                <Button
                  component={RouterLink}
                  to="/profile"
                  variant="outlined"
                  fullWidth
                  sx={{ py: 2 }}
                >
                  Profile
                </Button>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>
    </Container>
  );
};

export default Dashboard;
