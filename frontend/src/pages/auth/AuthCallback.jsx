import React, { useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { Box, CircularProgress, Typography, Alert } from '@mui/material';
import Cookies from 'js-cookie';
import { useAuth } from '../../contexts/AuthContext';
import { toast } from 'react-toastify';

const AuthCallback = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { loadUser } = useAuth();

  useEffect(() => {
    const handleCallback = async () => {
      const token = searchParams.get('token');
      const error = searchParams.get('error');

      if (error) {
        let errorMessage = 'Authentication failed';
        switch (error) {
          case 'authentication_failed':
            errorMessage = 'Authentication failed. Please try again.';
            break;
          case 'server_error':
            errorMessage = 'Server error occurred. Please try again later.';
            break;
          default:
            errorMessage = 'An unexpected error occurred during authentication.';
        }
        
        toast.error(errorMessage);
        navigate('/login');
        return;
      }

      if (token) {
        try {
          // Store the token
          Cookies.set('token', token, { expires: 7, secure: true, sameSite: 'strict' });
          
          // Load user data
          await loadUser();
          
          toast.success('Successfully logged in!');
          navigate('/dashboard');
        } catch (error) {
          console.error('Error processing authentication callback:', error);
          toast.error('Failed to complete authentication. Please try again.');
          navigate('/login');
        }
      } else {
        toast.error('No authentication token received.');
        navigate('/login');
      }
    };

    handleCallback();
  }, [searchParams, navigate, loadUser]);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        alignItems: 'center',
        justifyContent: 'center',
        minHeight: '80vh',
        textAlign: 'center',
      }}
    >
      <CircularProgress size={60} sx={{ mb: 3 }} />
      <Typography variant="h6" gutterBottom>
        Completing your sign in...
      </Typography>
      <Typography variant="body2" color="text.secondary">
        Please wait while we verify your credentials.
      </Typography>
    </Box>
  );
};

export default AuthCallback;
