import React from 'react';
import {
  <PERSON><PERSON>er,
  Ty<PERSON><PERSON>,
  Box,
  Button,
  Grid,
  Card,
  CardContent,
  CardMedia,
  Chip,
  Alert,
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { Search, Add, Explore } from '@mui/icons-material';
import { useAuth } from '../contexts/AuthContext';
import { healthCheck, authAPI } from '../services/api';

const Home = () => {
  const { user, isAuthenticated, isLoading, login } = useAuth();

  console.log('Home - user:', user, 'isAuthenticated:', isAuthenticated, 'isLoading:', isLoading);

  const testAPI = async () => {
    try {
      console.log('Testing API connection...');
      const response = await healthCheck();
      console.log('API Health Check Response:', response.data);
      alert('API is working! Check console for details.');
    } catch (error) {
      console.error('API Health Check Error:', error);
      alert('API connection failed! Check console for details.');
    }
  };

  const testRegistration = async () => {
    try {
      console.log('Testing registration...');
      const testUser = {
        firstName: 'Test',
        lastName: 'User',
        email: `test${Date.now()}@example.com`,
        password: 'Test123456',
        confirmPassword: 'Test123456'
      };
      const response = await authAPI.register(testUser);
      console.log('Registration Response:', response.data);
      alert('Registration test successful! Check console for details.');
    } catch (error) {
      console.error('Registration Test Error:', error);
      alert('Registration test failed! Check console for details.');
    }
  };

  const testLogin = async () => {
    try {
      console.log('Testing login...');
      const credentials = {
        email: '<EMAIL>',
        password: 'Test123456'
      };
      const response = await authAPI.login(credentials);
      console.log('Login Response:', response.data);
      alert('Login test successful! Check console for details.');
    } catch (error) {
      console.error('Login Test Error:', error);
      alert('Login test failed! Check console for details.');
    }
  };

  const testAuthContextLogin = async () => {
    try {
      console.log('Testing AuthContext login...');
      const result = await login({
        email: '<EMAIL>',
        password: 'Test123456'
      });
      console.log('AuthContext Login Result:', result);
      alert('AuthContext login test result: ' + (result.success ? 'SUCCESS' : 'FAILED'));
    } catch (error) {
      console.error('AuthContext Login Test Error:', error);
      alert('AuthContext login test failed! Check console for details.');
    }
  };

  const categories = [
    {
      title: 'Home Ganesha',
      description: 'Discover beautiful Ganesha installations in homes across the city',
      image: '/images/home-ganesha.jpg',
      path: '/ganesh?category=home',
      color: 'primary',
    },
    {
      title: 'Mandal Ganesha',
      description: 'Explore grand celebrations by various Ganesh mandals',
      image: '/images/mandal-ganesha.jpg',
      path: '/ganesh?category=mandal',
      color: 'secondary',
    },
    {
      title: 'Celebrity Ganesha',
      description: 'Visit famous personalities\' Ganesha celebrations',
      image: '/images/celebrity-ganesha.jpg',
      path: '/ganesh?category=celebrity',
      color: 'success',
    },
  ];

  return (
    <Box>
      {/* Debug Section */}
      {process.env.NODE_ENV === 'development' && (
        <Container maxWidth="lg" sx={{ py: 2 }}>
          <Alert severity="info" sx={{ mb: 2 }}>
            <Typography variant="body2">
              <strong>Debug Info:</strong>
              {isLoading ? ' Loading...' :
               isAuthenticated ? ` Logged in as ${user?.firstName} ${user?.lastName}` :
               ' Not logged in'}
            </Typography>
            <Box sx={{ mt: 1, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
              {isAuthenticated && (
                <Button
                  component={RouterLink}
                  to="/dashboard"
                  variant="contained"
                  size="small"
                >
                  Go to Dashboard
                </Button>
              )}
              <Button
                onClick={testAPI}
                variant="outlined"
                size="small"
              >
                Test API
              </Button>
              <Button
                onClick={testRegistration}
                variant="outlined"
                size="small"
              >
                Test Register
              </Button>
              <Button
                onClick={testLogin}
                variant="outlined"
                size="small"
              >
                Test Login
              </Button>
              <Button
                onClick={testAuthContextLogin}
                variant="outlined"
                size="small"
                color="secondary"
              >
                Test Auth Login
              </Button>
            </Box>
          </Alert>
        </Container>
      )}
      {/* Hero Section */}
      <Box
        sx={{
          background: 'linear-gradient(135deg, #FF6B35 0%, #FFC107 100%)',
          color: 'white',
          py: 8,
          textAlign: 'center',
          width: '100%',
        }}
      >
        <Container maxWidth="md" sx={{ mx: 'auto' }}>
          <Typography
            variant="h2"
            component="h1"
            gutterBottom
            fontWeight="bold"
            sx={{ fontSize: { xs: '2.5rem', md: '3.5rem' } }}
          >
            🙏 Ganesh Darshan
          </Typography>
          <Typography
            variant="h5"
            component="p"
            gutterBottom
            sx={{ mb: 4, opacity: 0.9 }}
          >
            Discover and share the divine presence of Lord Ganesha
          </Typography>
          <Typography
            variant="body1"
            sx={{ mb: 4, fontSize: '1.1rem', opacity: 0.8 }}
          >
            Connect with devotees, explore beautiful installations, and celebrate
            the festival of wisdom and prosperity together.
          </Typography>
          <Box sx={{ display: 'flex', gap: 2, justifyContent: 'center', flexWrap: 'wrap' }}>
            <Button
              component={RouterLink}
              to="/ganesh"
              variant="contained"
              size="large"
              startIcon={<Explore />}
              sx={{
                backgroundColor: 'white',
                color: 'primary.main',
                '&:hover': {
                  backgroundColor: 'grey.100',
                },
              }}
            >
              Explore Listings
            </Button>
            <Button
              component={RouterLink}
              to="/register"
              variant="outlined"
              size="large"
              startIcon={<Add />}
              sx={{
                borderColor: 'white',
                color: 'white',
                '&:hover': {
                  borderColor: 'white',
                  backgroundColor: 'rgba(255, 255, 255, 0.1)',
                },
              }}
            >
              Add Your Ganesha
            </Button>
          </Box>
        </Container>
      </Box>

      {/* Categories Section */}
      <Container maxWidth="lg" sx={{ py: 8 }}>
        <Box sx={{ textAlign: 'center', mb: 6 }}>
          <Typography variant="h3" component="h2" gutterBottom fontWeight="bold">
            Explore Categories
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ fontSize: '1.1rem' }}>
            Discover Ganesha installations across different categories
          </Typography>
        </Box>

        <Grid container spacing={4}>
          {categories.map((category, index) => (
            <Grid item xs={12} md={4} key={index}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  transition: 'transform 0.2s',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                  },
                }}
              >
                <CardMedia
                  component="div"
                  sx={{
                    height: 200,
                    background: `linear-gradient(45deg, ${
                      category.color === 'primary' ? '#FF6B35' : 
                      category.color === 'secondary' ? '#FFC107' : '#8BC34A'
                    } 30%, ${
                      category.color === 'primary' ? '#FF8A65' : 
                      category.color === 'secondary' ? '#FFD54F' : '#AED581'
                    } 90%)`,
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                  }}
                >
                  <Typography variant="h4" color="white" fontWeight="bold">
                    🙏
                  </Typography>
                </CardMedia>
                <CardContent sx={{ flexGrow: 1, p: 3 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h5" component="h3" fontWeight="bold" sx={{ flexGrow: 1 }}>
                      {category.title}
                    </Typography>
                    <Chip
                      label="Explore"
                      color={category.color}
                      size="small"
                    />
                  </Box>
                  <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
                    {category.description}
                  </Typography>
                  <Button
                    component={RouterLink}
                    to={category.path}
                    variant="outlined"
                    fullWidth
                    color={category.color}
                  >
                    View Listings
                  </Button>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      </Container>

      {/* CTA Section */}
      <Box sx={{ backgroundColor: 'grey.50', py: 8 }}>
        <Container maxWidth="md" sx={{ textAlign: 'center' }}>
          <Typography variant="h4" component="h2" gutterBottom fontWeight="bold">
            Share Your Ganesha with the World
          </Typography>
          <Typography variant="body1" color="text.secondary" sx={{ mb: 4, fontSize: '1.1rem' }}>
            Join thousands of devotees in celebrating Lord Ganesha. List your installation
            and connect with fellow devotees in your area.
          </Typography>
          <Button
            component={RouterLink}
            to="/register"
            variant="contained"
            size="large"
            startIcon={<Add />}
            sx={{ px: 4, py: 1.5 }}
          >
            Get Started Today
          </Button>
        </Container>
      </Box>
    </Box>
  );
};

export default Home;
