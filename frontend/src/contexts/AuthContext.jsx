import React, { createContext, useContext, useReducer, useEffect } from 'react';
import Cookies from 'js-cookie';
import { toast } from 'react-hot-toast';
import api from '../services/api';

// Auth context
const AuthContext = createContext();

// Auth actions
const AUTH_ACTIONS = {
  SET_LOADING: 'SET_LOADING',
  SET_USER: 'SET_USER',
  SET_ERROR: 'SET_ERROR',
  LOGOUT: 'LOGOUT'
};

// Auth reducer
const authReducer = (state, action) => {
  switch (action.type) {
    case AUTH_ACTIONS.SET_LOADING:
      return { ...state, isLoading: action.payload, error: null };
    case AUTH_ACTIONS.SET_USER:
      return { 
        ...state, 
        user: action.payload, 
        isAuthenticated: !!action.payload,
        isLoading: false,
        error: null 
      };
    case AUTH_ACTIONS.SET_ERROR:
      return { 
        ...state, 
        error: action.payload, 
        isLoading: false 
      };
    case AUTH_ACTIONS.LOGOUT:
      return { 
        user: null, 
        isAuthenticated: false, 
        isLoading: false, 
        error: null 
      };
    default:
      return state;
  }
};

// Initial state
const initialState = {
  user: null,
  isAuthenticated: false,
  isLoading: true,
  error: null
};

// Auth provider component
export const AuthProvider = ({ children }) => {
  const [state, dispatch] = useReducer(authReducer, initialState);

  // Load user on app start
  useEffect(() => {
    loadUser();
  }, []);

  // Load user from token
  const loadUser = async () => {
    try {
      const token = Cookies.get('token');
      console.log('Loading user, token:', token);
      
      if (!token) {
        dispatch({ type: AUTH_ACTIONS.SET_USER, payload: null });
        return;
      }

      const response = await api.get('/auth/me');
      console.log('Load user response:', response.data);
      
      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: response.data.data.user });
    } catch (error) {
      console.error('Load user error:', error);
      Cookies.remove('token');
      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: null });
    }
  };

  // Register function
  const register = async (userData) => {
    try {
      console.log('Starting registration:', userData);
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

      const response = await api.post('/auth/register', userData);
      console.log('Registration response:', response.data);

      toast.success('Registration successful! You can now login.');
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: false });
      
      return { success: true, data: response.data };
    } catch (error) {
      console.error('Registration error:', error);
      const message = error.response?.data?.message || 'Registration failed';
      toast.error(message);
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: message });
      
      return { success: false, error: message };
    }
  };

  // Login function
  const login = async (credentials) => {
    try {
      console.log('Starting login:', credentials);
      dispatch({ type: AUTH_ACTIONS.SET_LOADING, payload: true });

      const response = await api.post('/auth/login', credentials);
      console.log('Login response:', response.data);

      const { token, data } = response.data;
      const user = data.user;

      // Store token
      Cookies.set('token', token, { expires: 7 });
      console.log('Token stored:', token);

      // Set user
      dispatch({ type: AUTH_ACTIONS.SET_USER, payload: user });
      
      toast.success('Login successful!');
      
      return { success: true, user, token };
    } catch (error) {
      console.error('Login error:', error);
      const message = error.response?.data?.message || 'Login failed';
      toast.error(message);
      dispatch({ type: AUTH_ACTIONS.SET_ERROR, payload: message });
      
      return { success: false, error: message };
    }
  };

  // Logout function
  const logout = async () => {
    try {
      console.log('Logging out');
      
      // Remove token
      Cookies.remove('token');
      
      // Clear user state
      dispatch({ type: AUTH_ACTIONS.LOGOUT });
      
      toast.success('Logged out successfully');
      
      return { success: true };
    } catch (error) {
      console.error('Logout error:', error);
      return { success: false, error: 'Logout failed' };
    }
  };

  const value = {
    ...state,
    register,
    login,
    logout,
    loadUser
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};

// Custom hook to use auth context
export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export default AuthContext;
