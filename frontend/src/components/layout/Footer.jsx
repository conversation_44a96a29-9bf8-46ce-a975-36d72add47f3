import React from 'react';
import {
  Box,
  Container,
  Grid,
  Typo<PERSON>,
  Link,
  IconButton,
  Divider,
  useTheme,
} from '@mui/material';
import {
  Facebook,
  Twitter,
  Instagram,
  YouTube,
  Email,
  Phone,
  LocationOn,
} from '@mui/icons-material';
import { Link as RouterLink } from 'react-router-dom';

const Footer = () => {
  const theme = useTheme();
  const currentYear = new Date().getFullYear();

  const footerLinks = {
    explore: [
      { text: 'Home Ganesha', path: '/ganesh?category=home' },
      { text: 'Mandal Ganesha', path: '/ganesh?category=mandal' },
      { text: 'Celebrity Ganesha', path: '/ganesh?category=celebrity' },
      { text: 'Featured Listings', path: '/ganesh?featured=true' },
    ],
    company: [
      { text: 'About Us', path: '/about' },
      { text: 'Contact Us', path: '/contact' },
      { text: 'Privacy Policy', path: '/privacy' },
      { text: 'Terms of Service', path: '/terms' },
    ],
    support: [
      { text: 'Help Center', path: '/help' },
      { text: 'Community Guidelines', path: '/guidelines' },
      { text: 'Report an Issue', path: '/report' },
      { text: 'FAQ', path: '/faq' },
    ],
  };

  const socialLinks = [
    { icon: <Facebook />, url: 'https://facebook.com/ganeshdarshan', label: 'Facebook' },
    { icon: <Instagram />, url: 'https://instagram.com/ganeshdarshan', label: 'Instagram' },
    { icon: <Twitter />, url: 'https://twitter.com/ganeshdarshan', label: 'Twitter' },
    { icon: <YouTube />, url: 'https://youtube.com/ganeshdarshan', label: 'YouTube' },
  ];

  return (
    <Box
      component="footer"
      sx={{
        backgroundColor: theme.palette.grey[900],
        color: 'white',
        mt: 'auto',
        pt: 6,
        pb: 3,
      }}
    >
      <Container maxWidth="lg">
        <Grid container spacing={4}>
          {/* Brand Section */}
          <Grid item xs={12} md={4}>
            <Typography
              variant="h5"
              component="div"
              sx={{
                fontWeight: 'bold',
                mb: 2,
                color: theme.palette.primary.main,
              }}
            >
              🙏 Ganesh Darshan
            </Typography>
            <Typography variant="body2" sx={{ mb: 3, color: 'grey.300' }}>
              Discover and share the divine presence of Lord Ganesha. Connect with 
              devotees, explore beautiful installations, and celebrate the festival 
              of wisdom and prosperity together.
            </Typography>
            
            {/* Contact Info */}
            <Box sx={{ mb: 2 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Email sx={{ mr: 1, fontSize: 18 }} />
                <Typography variant="body2" color="grey.300">
                  <EMAIL>
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Phone sx={{ mr: 1, fontSize: 18 }} />
                <Typography variant="body2" color="grey.300">
                  +91 98765 43210
                </Typography>
              </Box>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LocationOn sx={{ mr: 1, fontSize: 18 }} />
                <Typography variant="body2" color="grey.300">
                  Mumbai, Maharashtra, India
                </Typography>
              </Box>
            </Box>

            {/* Social Links */}
            <Box>
              {socialLinks.map((social) => (
                <IconButton
                  key={social.label}
                  component="a"
                  href={social.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  sx={{
                    color: 'grey.400',
                    mr: 1,
                    '&:hover': {
                      color: theme.palette.primary.main,
                    },
                  }}
                  aria-label={social.label}
                >
                  {social.icon}
                </IconButton>
              ))}
            </Box>
          </Grid>

          {/* Explore Links */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              Explore
            </Typography>
            <Box>
              {footerLinks.explore.map((link) => (
                <Link
                  key={link.text}
                  component={RouterLink}
                  to={link.path}
                  sx={{
                    display: 'block',
                    color: 'grey.300',
                    textDecoration: 'none',
                    mb: 1,
                    '&:hover': {
                      color: theme.palette.primary.main,
                    },
                  }}
                >
                  {link.text}
                </Link>
              ))}
            </Box>
          </Grid>

          {/* Company Links */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              Company
            </Typography>
            <Box>
              {footerLinks.company.map((link) => (
                <Link
                  key={link.text}
                  component={RouterLink}
                  to={link.path}
                  sx={{
                    display: 'block',
                    color: 'grey.300',
                    textDecoration: 'none',
                    mb: 1,
                    '&:hover': {
                      color: theme.palette.primary.main,
                    },
                  }}
                >
                  {link.text}
                </Link>
              ))}
            </Box>
          </Grid>

          {/* Support Links */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              Support
            </Typography>
            <Box>
              {footerLinks.support.map((link) => (
                <Link
                  key={link.text}
                  component={RouterLink}
                  to={link.path}
                  sx={{
                    display: 'block',
                    color: 'grey.300',
                    textDecoration: 'none',
                    mb: 1,
                    '&:hover': {
                      color: theme.palette.primary.main,
                    },
                  }}
                >
                  {link.text}
                </Link>
              ))}
            </Box>
          </Grid>

          {/* Newsletter Signup */}
          <Grid item xs={12} sm={6} md={2}>
            <Typography variant="h6" sx={{ mb: 2, fontWeight: 'bold' }}>
              Stay Connected
            </Typography>
            <Typography variant="body2" sx={{ mb: 2, color: 'grey.300' }}>
              Get updates about new listings and festival events.
            </Typography>
            <Typography variant="body2" sx={{ color: 'grey.400' }}>
              Newsletter coming soon!
            </Typography>
          </Grid>
        </Grid>

        <Divider sx={{ my: 4, borderColor: 'grey.700' }} />

        {/* Bottom Section */}
        <Grid container spacing={2} alignItems="center">
          <Grid item xs={12} md={6}>
            <Typography variant="body2" color="grey.400" sx={{ textAlign: { xs: 'center', md: 'left' } }}>
              © {currentYear} Ganesh Darshan. All rights reserved.
            </Typography>
          </Grid>
          <Grid item xs={12} md={6}>
            <Box sx={{
              display: 'flex',
              justifyContent: { xs: 'center', md: 'flex-end' },
              flexWrap: 'wrap',
              gap: 1
            }}>
              <Typography variant="body2" color="grey.400">
                Made with ❤️ for devotees
              </Typography>
              <Typography variant="body2" color="grey.500">
                Ganpati Bappa Morya! 🙏
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Container>
    </Box>
  );
};

export default Footer;
