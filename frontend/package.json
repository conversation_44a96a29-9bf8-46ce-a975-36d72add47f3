{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite", "build": "vite build", "lint": "eslint .", "preview": "vite preview"}, "dependencies": {"@emotion/react": "^11.11.1", "@emotion/styled": "^11.11.0", "@hookform/resolvers": "^3.3.1", "@mui/icons-material": "^5.14.3", "@mui/material": "^5.14.5", "@tanstack/react-query": "^5.0.0", "axios": "^1.5.0", "js-cookie": "^3.0.5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-hook-form": "^7.45.4", "react-hot-toast": "^2.5.2", "react-router-dom": "^6.15.0", "react-toastify": "^9.1.3", "yup": "^1.3.2"}, "devDependencies": {"@eslint/js": "^9.29.0", "@types/js-cookie": "^3.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.5.2", "eslint": "^9.29.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.20", "globals": "^16.2.0", "vite": "^7.0.0"}}