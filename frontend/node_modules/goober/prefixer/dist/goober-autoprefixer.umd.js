!function(i,n){"object"==typeof exports&&"undefined"!=typeof module?n(exports):"function"==typeof define&&define.amd?define(["exports"],n):n((i||self).gooberPrefixer={})}(this,function(i){var n=new Map([["align-self","-ms-grid-row-align"],["color-adjust","-webkit-print-color-adjust"],["column-gap","grid-column-gap"],["gap","grid-gap"],["grid-template-columns","-ms-grid-columns"],["grid-template-rows","-ms-grid-rows"],["justify-self","-ms-grid-column-align"],["margin-inline-end","-webkit-margin-end"],["margin-inline-start","-webkit-margin-start"],["overflow-wrap","word-wrap"],["padding-inline-end","-webkit-padding-end"],["padding-inline-start","-webkit-padding-start"],["row-gap","grid-row-gap"],["scroll-margin-bottom","scroll-snap-margin-bottom"],["scroll-margin-left","scroll-snap-margin-left"],["scroll-margin-right","scroll-snap-margin-right"],["scroll-margin-top","scroll-snap-margin-top"],["scroll-margin","scroll-snap-margin"],["text-combine-upright","-ms-text-combine-horizontal"]]);i.prefix=function(i,t){let r="";const e=n.get(i);e&&(r+=`${e}:${t};`);const o=function(i){var n=/^(?:(text-(?:decoration$|e|or|si)|back(?:ground-cl|d|f)|box-d|(?:mask(?:$|-[ispro]|-cl)))|(tab-|column(?!-s)|text-align-l)|(ap)|(u|hy))/i.exec(i);return n?n[1]?1:n[2]?2:n[3]?3:5:0}(i);1&o&&(r+=`-webkit-${i}:${t};`),2&o&&(r+=`-moz-${i}:${t};`),4&o&&(r+=`-ms-${i}:${t};`);const a=function(i,n){var t=/^(?:(pos)|(background-i)|((?:max-|min-)?(?:block-s|inl|he|widt))|(dis))/i.exec(i);return t?t[1]?/^sti/i.test(n)?1:0:t[2]?/^image-/i.test(n)?1:0:t[3]?"-"===n[3]?2:0:/^(inline-)?grid$/i.test(n)?4:0:0}(i,t);return 1&a?r+=`${i}:-webkit-${t};`:2&a?r+=`${i}:-moz-${t};`:4&a&&(r+=`${i}:-ms-${t};`),r+=`${i}:${t};`,r}});
