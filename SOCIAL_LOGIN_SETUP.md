# Social Login Setup Guide

This guide explains how to set up Google and Facebook OAuth for the Ganesh Darshan application.

## Google OAuth Setup

### 1. Create Google Cloud Project
1. Go to [Google Cloud Console](https://console.cloud.google.com/)
2. Create a new project or select existing one
3. Enable the Google+ API

### 2. Create OAuth 2.0 Credentials
1. Go to "Credentials" in the left sidebar
2. Click "Create Credentials" → "OAuth 2.0 Client IDs"
3. Configure the consent screen first if prompted
4. Choose "Web application" as application type
5. Add authorized redirect URIs:
   - `http://localhost:5001/api/auth/google/callback` (development)
   - `https://yourdomain.com/api/auth/google/callback` (production)

### 3. Update Environment Variables
```bash
GOOGLE_CLIENT_ID=your-google-client-id-here
GOOGLE_CLIENT_SECRET=your-google-client-secret-here
```

## Facebook OAuth Setup

### 1. Create Facebook App
1. Go to [Facebook Developers](https://developers.facebook.com/)
2. Click "Create App"
3. Choose "Consumer" as app type
4. Fill in app details

### 2. Configure Facebook Login
1. In your app dashboard, go to "Products" → "Facebook Login"
2. Click "Settings" under Facebook Login
3. Add Valid OAuth Redirect URIs:
   - `http://localhost:5001/api/auth/facebook/callback` (development)
   - `https://yourdomain.com/api/auth/facebook/callback` (production)

### 3. Update Environment Variables
```bash
FACEBOOK_APP_ID=your-facebook-app-id-here
FACEBOOK_APP_SECRET=your-facebook-app-secret-here
```

## Testing Social Login

### Backend Routes Available:
- `GET /api/auth/google` - Initiate Google OAuth
- `GET /api/auth/google/callback` - Google OAuth callback
- `GET /api/auth/facebook` - Initiate Facebook OAuth
- `GET /api/auth/facebook/callback` - Facebook OAuth callback

### Frontend Integration:
```javascript
// Google Login
const handleGoogleLogin = () => {
  window.location.href = 'http://localhost:5001/api/auth/google';
};

// Facebook Login
const handleFacebookLogin = () => {
  window.location.href = 'http://localhost:5001/api/auth/facebook';
};
```

### Callback Handling:
After successful authentication, users will be redirected to:
`http://localhost:3000/auth/callback?token=JWT_TOKEN`

The frontend should:
1. Extract the token from URL parameters
2. Store the token in localStorage or cookies
3. Redirect user to dashboard/home page
4. Use the token for subsequent API calls

## Security Notes

1. **Environment Variables**: Never commit actual credentials to version control
2. **HTTPS**: Always use HTTPS in production
3. **Domain Validation**: Configure proper redirect URIs for production
4. **Token Security**: Store JWT tokens securely (httpOnly cookies recommended)

## Troubleshooting

### Common Issues:
1. **Invalid Redirect URI**: Ensure redirect URIs match exactly in OAuth provider settings
2. **App Not Approved**: Facebook apps need approval for public use
3. **Scope Issues**: Ensure proper scopes are requested (email, profile)
4. **CORS Issues**: Configure CORS properly for cross-origin requests

### Testing:
1. Test with development URLs first
2. Use browser developer tools to debug OAuth flow
3. Check server logs for detailed error messages
4. Verify environment variables are loaded correctly

## Production Deployment

1. Update redirect URIs to production domains
2. Set NODE_ENV=production
3. Use secure cookies and HTTPS
4. Configure proper CORS origins
5. Set up proper error handling and logging
