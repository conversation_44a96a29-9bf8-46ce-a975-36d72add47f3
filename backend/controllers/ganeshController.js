const { Ganesh } = require('../models');
const Email = require('../utils/email');

// Get all Ganesh listings
exports.getAllGanesh = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    // Build filter object
    const filter = { status: 'approved', isActive: true };
    
    if (req.query.category) {
      filter.category = req.query.category;
    }
    
    if (req.query.city) {
      filter['location.city'] = new RegExp(req.query.city, 'i');
    }

    const ganeshListings = await Ganesh.find(filter)
      .populate('owner', 'firstName lastName avatar')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Ganesh.countDocuments(filter);

    res.status(200).json({
      status: 'success',
      results: ganeshListings.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        ganeshListings
      }
    });
  } catch (error) {
    console.error('Get all Ganesh error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Search Ganesh listings
exports.searchGanesh = async (req, res) => {
  try {
    const { q, category, city, state } = req.query;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    // Build search query
    const searchQuery = {
      status: 'approved',
      isActive: true
    };

    if (q) {
      searchQuery.$text = { $search: q };
    }

    if (category) {
      searchQuery.category = category;
    }

    if (city) {
      searchQuery['location.city'] = new RegExp(city, 'i');
    }

    if (state) {
      searchQuery['location.state'] = new RegExp(state, 'i');
    }

    const ganeshListings = await Ganesh.find(searchQuery)
      .populate('owner', 'firstName lastName avatar')
      .skip(skip)
      .limit(limit)
      .sort(q ? { score: { $meta: 'textScore' } } : { createdAt: -1 });

    const total = await Ganesh.countDocuments(searchQuery);

    res.status(200).json({
      status: 'success',
      results: ganeshListings.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        ganeshListings
      }
    });
  } catch (error) {
    console.error('Search Ganesh error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Get featured Ganesh listings
exports.getFeaturedGanesh = async (req, res) => {
  try {
    const limit = parseInt(req.query.limit) || 6;

    const ganeshListings = await Ganesh.find({
      status: 'approved',
      isActive: true,
      isFeatured: true
    })
      .populate('owner', 'firstName lastName avatar')
      .limit(limit)
      .sort({ averageRating: -1, views: -1 });

    res.status(200).json({
      status: 'success',
      results: ganeshListings.length,
      data: {
        ganeshListings
      }
    });
  } catch (error) {
    console.error('Get featured Ganesh error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Get Ganesh listings by category
exports.getGaneshByCategory = async (req, res) => {
  try {
    const { category } = req.params;
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 12;
    const skip = (page - 1) * limit;

    if (!['home', 'mandal', 'celebrity'].includes(category)) {
      return res.status(400).json({
        status: 'fail',
        message: 'Invalid category. Must be home, mandal, or celebrity'
      });
    }

    const ganeshListings = await Ganesh.find({
      category,
      status: 'approved',
      isActive: true
    })
      .populate('owner', 'firstName lastName avatar')
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Ganesh.countDocuments({
      category,
      status: 'approved',
      isActive: true
    });

    res.status(200).json({
      status: 'success',
      results: ganeshListings.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        ganeshListings
      }
    });
  } catch (error) {
    console.error('Get Ganesh by category error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Get single Ganesh listing
exports.getGanesh = async (req, res) => {
  try {
    const ganesh = await Ganesh.findById(req.params.id)
      .populate('owner', 'firstName lastName avatar email phone')
      .populate('reviews.user', 'firstName lastName avatar');

    if (!ganesh || !ganesh.isActive) {
      return res.status(404).json({
        status: 'fail',
        message: 'No Ganesh listing found with that ID'
      });
    }

    // Increment view count
    ganesh.views += 1;
    await ganesh.save({ validateBeforeSave: false });

    res.status(200).json({
      status: 'success',
      data: {
        ganesh
      }
    });
  } catch (error) {
    console.error('Get Ganesh error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Get Ganesh listing by slug
exports.getGaneshBySlug = async (req, res) => {
  try {
    const ganesh = await Ganesh.findOne({ slug: req.params.slug })
      .populate('owner', 'firstName lastName avatar email phone')
      .populate('reviews.user', 'firstName lastName avatar');

    if (!ganesh || !ganesh.isActive) {
      return res.status(404).json({
        status: 'fail',
        message: 'No Ganesh listing found with that slug'
      });
    }

    // Increment view count
    ganesh.views += 1;
    await ganesh.save({ validateBeforeSave: false });

    res.status(200).json({
      status: 'success',
      data: {
        ganesh
      }
    });
  } catch (error) {
    console.error('Get Ganesh by slug error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Create new Ganesh listing
exports.createGanesh = async (req, res) => {
  try {
    // Add owner to the request body
    req.body.owner = req.user.id;

    const ganesh = await Ganesh.create(req.body);

    res.status(201).json({
      status: 'success',
      message: 'Ganesh listing created successfully! It will be reviewed before going live.',
      data: {
        ganesh
      }
    });
  } catch (error) {
    console.error('Create Ganesh error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Get user's own listings
exports.getMyListings = async (req, res) => {
  try {
    const page = parseInt(req.query.page) || 1;
    const limit = parseInt(req.query.limit) || 10;
    const skip = (page - 1) * limit;

    const ganeshListings = await Ganesh.find({ owner: req.user.id })
      .skip(skip)
      .limit(limit)
      .sort({ createdAt: -1 });

    const total = await Ganesh.countDocuments({ owner: req.user.id });

    res.status(200).json({
      status: 'success',
      results: ganeshListings.length,
      pagination: {
        page,
        limit,
        total,
        pages: Math.ceil(total / limit)
      },
      data: {
        ganeshListings
      }
    });
  } catch (error) {
    console.error('Get my listings error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Update Ganesh listing
exports.updateGanesh = async (req, res) => {
  try {
    const ganesh = await Ganesh.findOne({ _id: req.params.id, owner: req.user.id });

    if (!ganesh) {
      return res.status(404).json({
        status: 'fail',
        message: 'No Ganesh listing found with that ID or you do not have permission to update it'
      });
    }

    // Don't allow updating certain fields
    delete req.body.owner;
    delete req.body.status;
    delete req.body.views;
    delete req.body.likes;
    delete req.body.reviews;

    const updatedGanesh = await Ganesh.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true
    });

    res.status(200).json({
      status: 'success',
      message: 'Ganesh listing updated successfully',
      data: {
        ganesh: updatedGanesh
      }
    });
  } catch (error) {
    console.error('Update Ganesh error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Delete Ganesh listing
exports.deleteGanesh = async (req, res) => {
  try {
    const ganesh = await Ganesh.findOne({ _id: req.params.id, owner: req.user.id });

    if (!ganesh) {
      return res.status(404).json({
        status: 'fail',
        message: 'No Ganesh listing found with that ID or you do not have permission to delete it'
      });
    }

    await Ganesh.findByIdAndUpdate(req.params.id, { isActive: false });

    res.status(204).json({
      status: 'success',
      message: 'Ganesh listing deleted successfully',
      data: null
    });
  } catch (error) {
    console.error('Delete Ganesh error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Add review
exports.addReview = async (req, res) => {
  try {
    const { rating, comment } = req.body;

    if (!rating || rating < 1 || rating > 5) {
      return res.status(400).json({
        status: 'fail',
        message: 'Rating must be between 1 and 5'
      });
    }

    const ganesh = await Ganesh.findById(req.params.id);
    if (!ganesh) {
      return res.status(404).json({
        status: 'fail',
        message: 'No Ganesh listing found with that ID'
      });
    }

    // Check if user already reviewed
    const existingReview = ganesh.reviews.find(review =>
      review.user.toString() === req.user.id
    );

    if (existingReview) {
      return res.status(400).json({
        status: 'fail',
        message: 'You have already reviewed this listing'
      });
    }

    ganesh.reviews.push({
      user: req.user.id,
      rating,
      comment
    });

    await ganesh.save();

    res.status(201).json({
      status: 'success',
      message: 'Review added successfully',
      data: {
        ganesh
      }
    });
  } catch (error) {
    console.error('Add review error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Update review
exports.updateReview = async (req, res) => {
  try {
    const ganesh = await Ganesh.findById(req.params.id);
    if (!ganesh) {
      return res.status(404).json({
        status: 'fail',
        message: 'No Ganesh listing found with that ID'
      });
    }

    const review = ganesh.reviews.id(req.params.reviewId);
    if (!review) {
      return res.status(404).json({
        status: 'fail',
        message: 'No review found with that ID'
      });
    }

    if (review.user.toString() !== req.user.id) {
      return res.status(403).json({
        status: 'fail',
        message: 'You can only update your own reviews'
      });
    }

    if (req.body.rating) review.rating = req.body.rating;
    if (req.body.comment) review.comment = req.body.comment;

    await ganesh.save();

    res.status(200).json({
      status: 'success',
      message: 'Review updated successfully',
      data: {
        ganesh
      }
    });
  } catch (error) {
    console.error('Update review error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};

// Delete review
exports.deleteReview = async (req, res) => {
  try {
    const ganesh = await Ganesh.findById(req.params.id);
    if (!ganesh) {
      return res.status(404).json({
        status: 'fail',
        message: 'No Ganesh listing found with that ID'
      });
    }

    const review = ganesh.reviews.id(req.params.reviewId);
    if (!review) {
      return res.status(404).json({
        status: 'fail',
        message: 'No review found with that ID'
      });
    }

    if (review.user.toString() !== req.user.id && req.user.role !== 'admin') {
      return res.status(403).json({
        status: 'fail',
        message: 'You can only delete your own reviews'
      });
    }

    ganesh.reviews.pull(req.params.reviewId);
    await ganesh.save();

    res.status(204).json({
      status: 'success',
      message: 'Review deleted successfully',
      data: null
    });
  } catch (error) {
    console.error('Delete review error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Something went wrong'
    });
  }
};
