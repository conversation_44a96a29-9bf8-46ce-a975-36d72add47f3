const jwt = require('jsonwebtoken');
const { validationResult } = require('express-validator');
const User = require('../models/User');

// Helper function to generate JWT token
const generateToken = (id) => {
  return jwt.sign({ id }, process.env.JWT_SECRET, {
    expiresIn: process.env.JWT_EXPIRES_IN || '7d'
  });
};

// Register new user
exports.register = async (req, res) => {
  try {
    console.log('Registration request:', req.body);

    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Validation errors:', errors.array());
      return res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { firstName, lastName, email, password, phone } = req.body;

    // Check if user already exists
    const existingUser = await User.findOne({ email });
    if (existingUser) {
      console.log('User already exists:', email);
      return res.status(400).json({
        status: 'error',
        message: 'User with this email already exists'
      });
    }

    // Create new user
    const newUser = await User.create({
      firstName,
      lastName,
      email,
      password,
      phone,
      isEmailVerified: true,
      isActive: true
    });

    console.log('User created successfully:', newUser.email);

    // Generate token
    const token = generateToken(newUser._id);

    // Remove password from response
    const userResponse = {
      id: newUser._id,
      firstName: newUser.firstName,
      lastName: newUser.lastName,
      email: newUser.email,
      phone: newUser.phone,
      isEmailVerified: newUser.isEmailVerified,
      isActive: newUser.isActive,
      role: newUser.role,
      createdAt: newUser.createdAt
    };

    console.log('Registration successful, sending response');

    res.status(201).json({
      status: 'success',
      message: 'Registration successful! You can now login.',
      token,
      data: {
        user: userResponse
      }
    });
  } catch (error) {
    console.error('Registration error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Registration failed. Please try again.'
    });
  }
};

// Login user
exports.login = async (req, res) => {
  try {
    console.log('Login request:', req.body);

    // Check for validation errors
    const errors = validationResult(req);
    if (!errors.isEmpty()) {
      console.log('Validation errors:', errors.array());
      return res.status(400).json({
        status: 'error',
        message: 'Validation failed',
        errors: errors.array()
      });
    }

    const { email, password } = req.body;

    // Check if email and password exist
    if (!email || !password) {
      return res.status(400).json({
        status: 'error',
        message: 'Please provide email and password'
      });
    }

    // Check if user exists and password is correct
    const user = await User.findOne({ email }).select('+password');
    
    if (!user || !(await user.correctPassword(password, user.password))) {
      console.log('Invalid credentials for:', email);
      return res.status(401).json({
        status: 'error',
        message: 'Invalid email or password'
      });
    }

    // Check if user is active
    if (!user.isActive) {
      return res.status(401).json({
        status: 'error',
        message: 'Your account has been deactivated. Please contact support.'
      });
    }

    console.log('Login successful for:', email);

    // Update last login
    user.lastLogin = new Date();
    user.loginCount = (user.loginCount || 0) + 1;
    await user.save({ validateBeforeSave: false });

    // Generate token
    const token = generateToken(user._id);

    // Remove password from response
    const userResponse = {
      id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      isEmailVerified: user.isEmailVerified,
      isActive: user.isActive,
      role: user.role,
      avatar: user.avatar,
      lastLogin: user.lastLogin,
      loginCount: user.loginCount,
      createdAt: user.createdAt
    };

    res.status(200).json({
      status: 'success',
      message: 'Logged in successfully',
      token,
      data: {
        user: userResponse
      }
    });
  } catch (error) {
    console.error('Login error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Login failed. Please try again.'
    });
  }
};

// Get current user
exports.getMe = async (req, res) => {
  try {
    console.log('Get me request for user:', req.user.id);
    
    const user = await User.findById(req.user.id);
    
    if (!user) {
      return res.status(404).json({
        status: 'error',
        message: 'User not found'
      });
    }

    const userResponse = {
      id: user._id,
      firstName: user.firstName,
      lastName: user.lastName,
      email: user.email,
      phone: user.phone,
      isEmailVerified: user.isEmailVerified,
      isActive: user.isActive,
      role: user.role,
      avatar: user.avatar,
      lastLogin: user.lastLogin,
      loginCount: user.loginCount,
      createdAt: user.createdAt
    };

    res.status(200).json({
      status: 'success',
      data: {
        user: userResponse
      }
    });
  } catch (error) {
    console.error('Get me error:', error);
    res.status(500).json({
      status: 'error',
      message: 'Failed to get user data'
    });
  }
};

// Logout user
exports.logout = async (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Logged out successfully'
  });
};

// Refresh token (simplified - just return success)
exports.refreshToken = async (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Token refresh not implemented'
  });
};

// Email verification (simplified - just return success)
exports.verifyEmail = async (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Email verification not needed'
  });
};

// Resend verification (simplified - just return success)
exports.resendVerification = async (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Email verification not needed'
  });
};

// Forgot password (simplified - just return success)
exports.forgotPassword = async (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Password reset not implemented'
  });
};

// Reset password (simplified - just return success)
exports.resetPassword = async (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Password reset not implemented'
  });
};

// Update password (simplified - just return success)
exports.updatePassword = async (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Password update not implemented'
  });
};

// Social login success (simplified - just return success)
exports.socialLoginSuccess = async (req, res) => {
  res.status(200).json({
    status: 'success',
    message: 'Social login not implemented'
  });
};

// Health check
exports.healthCheck = async (req, res) => {
  res.status(200).json({
    status: 'OK',
    message: 'Ganesh Darshan API is running',
    timestamp: new Date().toISOString()
  });
};
