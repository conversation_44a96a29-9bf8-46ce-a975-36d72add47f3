const express = require('express');
const ganeshController = require('../controllers/ganeshController');
const { protect, restrictTo, requireEmailVerification, optionalAuth } = require('../middleware/auth');

const router = express.Router();

// Public routes
router.get('/', optionalAuth, ganeshController.getAllGanesh);
router.get('/search', ganeshController.searchGanesh);
router.get('/featured', ganeshController.getFeaturedGanesh);
router.get('/categories/:category', ganeshController.getGaneshByCategory);
router.get('/:id', optionalAuth, ganeshController.getGanesh);
router.get('/slug/:slug', optionalAuth, ganeshController.getGaneshBySlug);

// Protected routes - require authentication
router.use(protect);

// User routes (require email verification)
router.post('/', requireEmailVerification, ganeshController.createGanesh);
router.get('/my/listings', ganeshController.getMyListings);
router.patch('/:id', ganeshController.updateGanesh);
router.delete('/:id', ganeshController.deleteGanesh);

// Review and rating routes
router.post('/:id/reviews', requireEmailVerification, ganeshController.addReview);
router.patch('/:id/reviews/:reviewId', ganeshController.updateReview);
router.delete('/:id/reviews/:reviewId', ganeshController.deleteReview);

// Like/Unlike routes
router.post('/:id/like', ganeshController.likeGanesh);
router.delete('/:id/like', ganeshController.unlikeGanesh);

// Admin/Moderator routes
router.use(restrictTo('admin', 'moderator'));
router.patch('/:id/approve', ganeshController.approveGanesh);
router.patch('/:id/reject', ganeshController.rejectGanesh);
router.patch('/:id/feature', ganeshController.toggleFeature);

module.exports = router;
