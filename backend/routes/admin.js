const express = require('express');
const router = express.Router();
const { body } = require('express-validator');
const auth = require('../middleware/auth');
const adminController = require('../controllers/adminController');

// Middleware to check if user is admin
const isAdmin = (req, res, next) => {
  if (req.user.role !== 'admin' && req.user.role !== 'superadmin') {
    return res.status(403).json({
      status: 'error',
      message: 'Access denied. Admin privileges required.'
    });
  }
  next();
};

// Middleware to check if user is super admin
const isSuperAdmin = (req, res, next) => {
  if (req.user.role !== 'superadmin') {
    return res.status(403).json({
      status: 'error',
      message: 'Access denied. Super admin privileges required.'
    });
  }
  next();
};

// Admin dashboard stats
router.get('/dashboard/stats', auth, isAdmin, adminController.getDashboardStats);

// User management routes
router.get('/users', auth, isAdmin, adminController.getAllUsers);
router.put('/users/:userId/status', 
  auth, 
  isAdmin,
  [
    body('isActive').optional().isBoolean(),
    body('role').optional().isIn(['user', 'admin', 'superadmin']),
    body('isEmailVerified').optional().isBoolean()
  ],
  adminController.updateUserStatus
);
router.delete('/users/:userId', auth, isSuperAdmin, adminController.deleteUser);

// Listing management routes
router.get('/listings', auth, isAdmin, adminController.getAllListings);
router.put('/listings/:listingId/status',
  auth,
  isAdmin,
  [
    body('status').isIn(['pending', 'approved', 'rejected']),
    body('rejectionReason').optional().isString()
  ],
  adminController.updateListingStatus
);
router.delete('/listings/:listingId', auth, isAdmin, adminController.deleteListing);

module.exports = router;
