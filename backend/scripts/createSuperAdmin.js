const mongoose = require('mongoose');
const bcrypt = require('bcryptjs');
require('dotenv').config();

// Import User model
const User = require('../models/User');

const createSuperAdmin = async () => {
  try {
    // Connect to MongoDB
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/ganesh-darshan', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    
    console.log('Connected to MongoDB');
    
    // Check if super admin already exists
    const existingSuperAdmin = await User.findOne({ role: 'superadmin' });
    
    if (existingSuperAdmin) {
      console.log('Super admin already exists:', existingSuperAdmin.email);
      process.exit(0);
    }
    
    // Create super admin user
    const hashedPassword = await bcrypt.hash('SuperAdmin123!', 12);
    
    const superAdmin = new User({
      firstName: 'Super',
      lastName: 'Admin',
      email: '<EMAIL>',
      password: hashedPassword,
      role: 'superadmin',
      isEmailVerified: true,
      isActive: true,
      address: {
        country: 'India'
      },
      preferences: {
        notifications: {
          email: true,
          sms: false
        },
        privacy: {
          showEmail: false,
          showPhone: false
        }
      }
    });
    
    await superAdmin.save();
    
    console.log('Super admin created successfully!');
    console.log('Email: <EMAIL>');
    console.log('Password: SuperAdmin123!');
    console.log('Role: superadmin');
    
    process.exit(0);
    
  } catch (error) {
    console.error('Error creating super admin:', error);
    process.exit(1);
  }
};

createSuperAdmin();
