const nodemailer = require('nodemailer');

class Email {
  constructor(user, url) {
    this.to = user.email;
    this.firstName = user.firstName;
    this.url = url;
    this.from = `<PERSON><PERSON><PERSON> <${process.env.EMAIL_FROM}>`;
  }

  newTransport() {
    if (process.env.NODE_ENV === 'production') {
      // Use SendGrid in production
      return nodemailer.createTransporter({
        service: 'SendGrid',
        auth: {
          user: process.env.SENDGRID_USERNAME,
          pass: process.env.SENDGRID_PASSWORD
        }
      });
    }

    // Use Gmail SMTP for development
    return nodemailer.createTransport({
      host: process.env.EMAIL_HOST,
      port: process.env.EMAIL_PORT,
      secure: false, // true for 465, false for other ports
      auth: {
        user: process.env.EMAIL_USER,
        pass: process.env.EMAIL_PASS
      }
    });
  }

  // Send the actual email
  async send(template, subject) {
    // Define email options
    const mailOptions = {
      from: this.from,
      to: this.to,
      subject,
      html: template,
      text: template.replace(/<[^>]*>/g, '') // Strip HTML for text version
    };

    // Create a transport and send email
    await this.newTransport().sendMail(mailOptions);
  }

  async sendWelcome() {
    const template = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #FF6B35; margin: 0;">🙏 Ganesh Darshan</h1>
          <p style="color: #666; margin: 5px 0;">Welcome to the Divine Experience</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 10px; margin-bottom: 20px;">
          <h2 style="color: #333; margin-top: 0;">Welcome, ${this.firstName}!</h2>
          <p style="color: #555; line-height: 1.6;">
            Thank you for joining Ganesh Darshan! We're excited to have you as part of our community 
            celebrating the divine presence of Lord Ganesha.
          </p>
          <p style="color: #555; line-height: 1.6;">
            With your account, you can:
          </p>
          <ul style="color: #555; line-height: 1.8;">
            <li>Discover beautiful Ganesh installations near you</li>
            <li>List your own Ganesha for others to visit</li>
            <li>Connect with mandals and fellow devotees</li>
            <li>Get updates on special events and celebrations</li>
          </ul>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.CLIENT_URL}" 
             style="background: #FF6B35; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
            Explore Ganesh Darshan
          </a>
        </div>
        
        <div style="text-align: center; color: #888; font-size: 14px; margin-top: 30px;">
          <p>Ganpati Bappa Morya! 🙏</p>
          <p>The Ganesh Darshan Team</p>
        </div>
      </div>
    `;
    
    await this.send(template, 'Welcome to Ganesh Darshan! 🙏');
  }

  async sendEmailVerification() {
    const template = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #FF6B35; margin: 0;">🙏 Ganesh Darshan</h1>
          <p style="color: #666; margin: 5px 0;">Email Verification</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 10px; margin-bottom: 20px;">
          <h2 style="color: #333; margin-top: 0;">Verify Your Email Address</h2>
          <p style="color: #555; line-height: 1.6;">
            Hello ${this.firstName},
          </p>
          <p style="color: #555; line-height: 1.6;">
            Please click the button below to verify your email address and complete your registration:
          </p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${this.url}" 
             style="background: #FF6B35; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
            Verify Email Address
          </a>
        </div>
        
        <div style="background: #fff3cd; border: 1px solid #ffeaa7; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="color: #856404; margin: 0; font-size: 14px;">
            <strong>Note:</strong> This verification link will expire in 24 hours. If you didn't create an account with Ganesh Darshan, please ignore this email.
          </p>
        </div>
        
        <div style="text-align: center; color: #888; font-size: 14px; margin-top: 30px;">
          <p>If the button doesn't work, copy and paste this link into your browser:</p>
          <p style="word-break: break-all;">${this.url}</p>
          <p style="margin-top: 20px;">The Ganesh Darshan Team</p>
        </div>
      </div>
    `;
    
    await this.send(template, 'Verify your email address - Ganesh Darshan');
  }

  async sendPasswordReset() {
    const template = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #FF6B35; margin: 0;">🙏 Ganesh Darshan</h1>
          <p style="color: #666; margin: 5px 0;">Password Reset</p>
        </div>
        
        <div style="background: #f8f9fa; padding: 30px; border-radius: 10px; margin-bottom: 20px;">
          <h2 style="color: #333; margin-top: 0;">Reset Your Password</h2>
          <p style="color: #555; line-height: 1.6;">
            Hello ${this.firstName},
          </p>
          <p style="color: #555; line-height: 1.6;">
            You requested a password reset for your Ganesh Darshan account. Click the button below to reset your password:
          </p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${this.url}" 
             style="background: #FF6B35; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
            Reset Password
          </a>
        </div>
        
        <div style="background: #f8d7da; border: 1px solid #f5c6cb; padding: 15px; border-radius: 5px; margin: 20px 0;">
          <p style="color: #721c24; margin: 0; font-size: 14px;">
            <strong>Security Notice:</strong> This password reset link will expire in 10 minutes. If you didn't request a password reset, please ignore this email and your password will remain unchanged.
          </p>
        </div>
        
        <div style="text-align: center; color: #888; font-size: 14px; margin-top: 30px;">
          <p>If the button doesn't work, copy and paste this link into your browser:</p>
          <p style="word-break: break-all;">${this.url}</p>
          <p style="margin-top: 20px;">The Ganesh Darshan Team</p>
        </div>
      </div>
    `;
    
    await this.send(template, 'Reset your password - Ganesh Darshan');
  }

  async sendListingApproved(listingTitle) {
    const template = `
      <div style="font-family: Arial, sans-serif; max-width: 600px; margin: 0 auto; padding: 20px;">
        <div style="text-align: center; margin-bottom: 30px;">
          <h1 style="color: #FF6B35; margin: 0;">🙏 Ganesh Darshan</h1>
          <p style="color: #666; margin: 5px 0;">Listing Approved</p>
        </div>
        
        <div style="background: #d4edda; border: 1px solid #c3e6cb; padding: 30px; border-radius: 10px; margin-bottom: 20px;">
          <h2 style="color: #155724; margin-top: 0;">🎉 Great News!</h2>
          <p style="color: #155724; line-height: 1.6;">
            Hello ${this.firstName},
          </p>
          <p style="color: #155724; line-height: 1.6;">
            Your Ganesh listing "<strong>${listingTitle}</strong>" has been approved and is now live on Ganesh Darshan!
          </p>
          <p style="color: #155724; line-height: 1.6;">
            Devotees can now discover and visit your beautiful Ganesha installation.
          </p>
        </div>
        
        <div style="text-align: center; margin: 30px 0;">
          <a href="${process.env.CLIENT_URL}/my-listings" 
             style="background: #FF6B35; color: white; padding: 15px 30px; text-decoration: none; border-radius: 5px; display: inline-block; font-weight: bold;">
            View My Listings
          </a>
        </div>
        
        <div style="text-align: center; color: #888; font-size: 14px; margin-top: 30px;">
          <p>Thank you for sharing the divine presence with our community!</p>
          <p>Ganpati Bappa Morya! 🙏</p>
          <p>The Ganesh Darshan Team</p>
        </div>
      </div>
    `;
    
    await this.send(template, `Your listing "${listingTitle}" has been approved! 🎉`);
  }
}

module.exports = Email;
