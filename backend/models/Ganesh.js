const mongoose = require('mongoose');

const ganeshSchema = new mongoose.Schema({
  // Basic Information
  title: {
    type: String,
    required: [true, 'Title is required'],
    trim: true,
    maxlength: [100, 'Title cannot exceed 100 characters']
  },
  description: {
    type: String,
    required: [true, 'Description is required'],
    trim: true,
    maxlength: [2000, 'Description cannot exceed 2000 characters']
  },
  
  // Category
  category: {
    type: String,
    required: [true, 'Category is required'],
    enum: {
      values: ['home', 'mandal', 'celebrity'],
      message: 'Category must be either home, mandal, or celebrity'
    }
  },
  
  // Owner Information
  owner: {
    type: mongoose.Schema.Types.ObjectId,
    ref: 'User',
    required: [true, 'Owner is required']
  },
  
  // Location Information
  location: {
    address: {
      type: String,
      required: [true, 'Address is required'],
      trim: true
    },
    landmark: {
      type: String,
      trim: true
    },
    city: {
      type: String,
      required: [true, 'City is required'],
      trim: true
    },
    state: {
      type: String,
      required: [true, 'State is required'],
      trim: true
    },
    pincode: {
      type: String,
      required: [true, 'Pincode is required'],
      match: [/^[0-9]{6}$/, 'Please enter a valid 6-digit pincode']
    },
    coordinates: {
      latitude: {
        type: Number,
        min: [-90, 'Latitude must be between -90 and 90'],
        max: [90, 'Latitude must be between -90 and 90']
      },
      longitude: {
        type: Number,
        min: [-180, 'Longitude must be between -180 and 180'],
        max: [180, 'Longitude must be between -180 and 180']
      }
    }
  },
  
  // Images
  images: [{
    url: {
      type: String,
      required: true
    },
    publicId: String, // For Cloudinary
    caption: String,
    isPrimary: {
      type: Boolean,
      default: false
    }
  }],
  
  // Festival Details
  festivalDetails: {
    installationDate: {
      type: Date,
      required: [true, 'Installation date is required']
    },
    visarjanDate: {
      type: Date,
      required: [true, 'Visarjan date is required']
    },
    specialEvents: [{
      name: String,
      date: Date,
      time: String,
      description: String
    }],
    aarti: {
      morning: String, // Time format: "HH:MM"
      evening: String
    },
    prasad: {
      available: {
        type: Boolean,
        default: true
      },
      types: [String] // e.g., ['Modak', 'Laddu', 'Fruits']
    }
  },
  
  // Mandal Specific Information (for mandal category)
  mandalInfo: {
    name: {
      type: String,
      required: function() {
        return this.category === 'mandal';
      }
    },
    establishedYear: Number,
    presidentName: String,
    contactPerson: {
      name: String,
      phone: String,
      email: String
    },
    socialMedia: {
      facebook: String,
      instagram: String,
      twitter: String,
      youtube: String
    }
  },
  
  // Celebrity Specific Information (for celebrity category)
  celebrityInfo: {
    celebrityName: {
      type: String,
      required: function() {
        return this.category === 'celebrity';
      }
    },
    profession: String, // Actor, Politician, Businessman, etc.
    verified: {
      type: Boolean,
      default: false
    }
  },
  
  // Visitor Information
  visitingHours: {
    openTime: String, // "HH:MM"
    closeTime: String, // "HH:MM"
    specialNote: String
  },
  
  // Facilities
  facilities: [{
    type: String,
    enum: ['parking', 'wheelchair_accessible', 'restroom', 'food_stall', 'donation_box', 'security', 'first_aid']
  }],
  
  // Status and Moderation
  status: {
    type: String,
    enum: ['pending', 'approved', 'rejected', 'archived'],
    default: 'pending'
  },
  rejectionReason: {
    type: String,
    maxlength: [500, 'Rejection reason cannot exceed 500 characters']
  },
  isActive: {
    type: Boolean,
    default: true
  },
  isFeatured: {
    type: Boolean,
    default: false
  },
  
  // Engagement Metrics
  views: {
    type: Number,
    default: 0
  },
  likes: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Reviews and Ratings
  reviews: [{
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    rating: {
      type: Number,
      required: true,
      min: 1,
      max: 5
    },
    comment: {
      type: String,
      maxlength: [500, 'Review comment cannot exceed 500 characters']
    },
    createdAt: {
      type: Date,
      default: Date.now
    }
  }],
  
  // Calculated Fields
  averageRating: {
    type: Number,
    default: 0,
    min: 0,
    max: 5
  },
  totalReviews: {
    type: Number,
    default: 0
  },
  
  // SEO and Search
  tags: [String],
  slug: {
    type: String,
    unique: true
  }
}, {
  timestamps: true,
  toJSON: { virtuals: true },
  toObject: { virtuals: true }
});

// Indexes for better performance
ganeshSchema.index({ category: 1, status: 1 });
ganeshSchema.index({ 'location.city': 1, 'location.state': 1 });
ganeshSchema.index({ 'location.pincode': 1 });
ganeshSchema.index({ createdAt: -1 });
ganeshSchema.index({ averageRating: -1 });
ganeshSchema.index({ views: -1 });
ganeshSchema.index({ slug: 1 });
ganeshSchema.index({ tags: 1 });

// Text search index
ganeshSchema.index({
  title: 'text',
  description: 'text',
  'location.address': 'text',
  'location.city': 'text',
  'mandalInfo.name': 'text',
  'celebrityInfo.celebrityName': 'text'
});

// Virtual for like count
ganeshSchema.virtual('likeCount').get(function() {
  return this.likes.length;
});

// Pre-save middleware to generate slug
ganeshSchema.pre('save', function(next) {
  if (this.isModified('title')) {
    this.slug = this.title
      .toLowerCase()
      .replace(/[^a-zA-Z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '') + '-' + this._id.toString().slice(-6);
  }
  next();
});

// Pre-save middleware to calculate average rating
ganeshSchema.pre('save', function(next) {
  if (this.reviews.length > 0) {
    const sum = this.reviews.reduce((acc, review) => acc + review.rating, 0);
    this.averageRating = Math.round((sum / this.reviews.length) * 10) / 10;
    this.totalReviews = this.reviews.length;
  }
  next();
});

module.exports = mongoose.model('Ganesh', ganeshSchema);
